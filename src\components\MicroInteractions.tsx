import React, { useEffect, useRef, useState } from 'react';

interface MicroInteractionProps {
  children: React.ReactNode;
  type?: 'hover' | 'click' | 'scroll' | 'magnetic' | 'tilt' | 'ripple';
  intensity?: 'subtle' | 'medium' | 'strong';
  delay?: number;
  className?: string;
  disabled?: boolean;
}

const MicroInteractions: React.FC<MicroInteractionProps> = ({
  children,
  type = 'hover',
  delay = 100,
  intensity = 'medium',
  className = '',
  disabled = false,
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [isInteracting, setIsInteracting] = useState(false);
  const [isVisible, setIsVisible] = useState(type !== 'scroll'); // only track visibility for scroll
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const delayTimeout = useRef<number | null>(null);

  useEffect(() => {
    if (disabled) return;

    const element = elementRef.current;
    if (!element) return;

    // Handle scroll reveal
    if (type === 'scroll') {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            if (delayTimeout.current) clearTimeout(delayTimeout.current);
            delayTimeout.current = window.setTimeout(() => {
              setIsVisible(true);
            }, delay);
          }
        },
        { threshold: 0.2 }
      );

      observer.observe(element);

      return () => {
        observer.disconnect();
        if (delayTimeout.current) clearTimeout(delayTimeout.current);
      };
    }

    // Handle mouse-based interactions
    const handleMouseMove = (e: MouseEvent) => {
      if (type === 'magnetic' || type === 'tilt') {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const deltaX = e.clientX - centerX;
        const deltaY = e.clientY - centerY;

        setMousePosition({ x: deltaX, y: deltaY });
      }
    };

    const handleMouseEnter = () => {
      if (delayTimeout.current) clearTimeout(delayTimeout.current);
      delayTimeout.current = window.setTimeout(() => {
        setIsInteracting(true);
        if (type === 'magnetic' || type === 'tilt') {
          document.addEventListener('mousemove', handleMouseMove);
        }
      }, delay);
    };

    const handleMouseLeave = () => {
      if (delayTimeout.current) clearTimeout(delayTimeout.current);
      setIsInteracting(false);
      setMousePosition({ x: 0, y: 0 });
      if (type === 'magnetic' || type === 'tilt') {
        document.removeEventListener('mousemove', handleMouseMove);
      }
    };

    const handleClick = (e: MouseEvent) => {
      if (type === 'ripple') {
        createRippleEffect(e);
      }
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    element.addEventListener('click', handleClick);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      element.removeEventListener('click', handleClick);
      document.removeEventListener('mousemove', handleMouseMove);
      if (delayTimeout.current) clearTimeout(delayTimeout.current);
    };
  }, [type, disabled, delay]);

  const createRippleEffect = (e: MouseEvent) => {
    const element = elementRef.current;
    if (!element) return;

    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    const ripple = document.createElement('div');
    ripple.className = 'luxury-ripple-effect';
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: radial-gradient(circle, rgba(212, 175, 55, 0.3) 0%, transparent 70%);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple-animation 0.6s ease-out;
      pointer-events: none;
      z-index: 1;
    `;

    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);

    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, 600);
  };

  const getTransformStyle = () => {
    if (disabled || !isInteracting) return {};

    const intensityMultiplier = {
      subtle: 0.5,
      medium: 1,
      strong: 1.5,
    }[intensity];

    switch (type) {
      case 'magnetic':
        return {
          transform: `translate(${mousePosition.x * 0.1 * intensityMultiplier}px, ${mousePosition.y * 0.1 * intensityMultiplier}px)`,
          transition: 'transform 0.2s ease-out',
        };

      case 'tilt': {
        const maxTilt = 15 * intensityMultiplier;
        const tiltX = (mousePosition.y / 100) * maxTilt;
        const tiltY = (mousePosition.x / 100) * -maxTilt;
        return {
          transform: `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg)`,
          transition: 'transform 0.2s ease-out',
        };
      }

      case 'hover':
        return {
          transform: `translateY(-${4 * intensityMultiplier}px) scale(${1 + 0.02 * intensityMultiplier})`,
          transition: 'transform 0.3s ease-out',
        };

      case 'scroll':
        return {
          transform: isInteracting ? 'translateY(0)' : 'translateY(20px)',
          opacity: isInteracting ? 1 : 0,
          transition: 'all 0.6s ease-out',
        };

      default:
        return {};
    }
  };

  const getClassName = () => {
    const baseClasses = ['enhanced-micro-interaction', className];

    if (isInteracting) {
      baseClasses.push('interacting');
    }

    if (type === 'scroll') {
      baseClasses.push(isVisible ? 'visible' : 'invisible opacity-0');
    }

    baseClasses.push(`interaction-${type}`);
    baseClasses.push(`intensity-${intensity}`);

    return baseClasses.filter(Boolean).join(' ');
  };

  return (
    <div
      ref={elementRef}
      className={getClassName()}
      style={getTransformStyle()}
    >
      {children}
    </div>
  );
};

export default MicroInteractions;
