import { Alert<PERSON>riangle, Home, Mail, RefreshCw } from 'lucide-react';
import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
}

class ErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);

    this.setState({
      errorInfo,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Log to error reporting service in production
    if (import.meta.env.PROD) {
      this.reportError(error, errorInfo);
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      level: this.props.level || 'component',
    };

    // In development, log to console
    if (import.meta.env.DEV) {
      console.group('🚨 Error Report');
      console.error('Error ID:', errorReport.errorId);
      console.error('Message:', errorReport.message);
      console.error('Level:', errorReport.level);
      console.groupEnd();
    }
  };

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: '',
        retryCount: prevState.retryCount + 1,
      }));
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { level = 'page' } = this.props;
      const { error, errorId, retryCount } = this.state;

      // Enhanced error UI based on level
      if (level === 'page') {
        return (
          <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black flex items-center justify-center px-4">
            <div className="max-w-md w-full text-center">
              <div className="mb-8">
                <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h1 className="text-3xl font-bold text-white mb-2">Something went wrong</h1>
                <p className="text-gray-400">
                  We're sorry, but something unexpected happened. Our team has been notified.
                </p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 mb-6 text-left">
                <p className="text-sm text-gray-500 mb-2">Error ID: {errorId}</p>
                <p className="text-sm text-red-400 font-mono">
                  {error?.message || 'Unknown error occurred'}
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={this.handleRetry}
                  disabled={retryCount >= this.maxRetries}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg transition-colors">
                  <RefreshCw className="w-4 h-4" />
                  <span>Try Again ({this.maxRetries - retryCount} attempts left)</span>
                </button>

                <button
                  onClick={this.handleGoHome}
                  className="w-full flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors">
                  <Home className="w-4 h-4" />
                  <span>Go to Homepage</span>
                </button>

                <button
                  onClick={this.handleReload}
                  className="w-full flex items-center justify-center space-x-2 bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors">
                  <RefreshCw className="w-4 h-4" />
                  <span>Reload Page</span>
                </button>
              </div>

              <div className="mt-8 pt-6 border-t border-gray-700">
                <p className="text-sm text-gray-500 mb-2">Need help?</p>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors">
                  <Mail className="w-4 h-4" />
                  <span>Contact Support</span>
                </a>
              </div>

              {import.meta.env.DEV && error && this.props.showDetails && (
                <details className="mt-8 text-left">
                  <summary className="cursor-pointer text-red-400 mb-2">
                    Error Details (Development Only)
                  </summary>
                  <pre className="bg-gray-900 p-4 rounded text-xs overflow-auto">
                    {error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </details>
              )}
            </div>
          </div>
        );
      }

      // Component-level error UI
      return (
        <div className="bg-red-50 border border-red-200 rounded p-4 my-2">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-sm text-red-800">Component failed to load</span>
            <button
              onClick={this.handleRetry}
              disabled={retryCount >= this.maxRetries}
              className="ml-auto text-xs bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-2 py-1 rounded transition-colors">
              Retry
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
