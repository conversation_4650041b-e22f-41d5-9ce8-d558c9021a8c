import React, { useEffect, useState } from 'react';

const LoadingScreen: React.FC<{ onComplete: () => void }> = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(onComplete, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 25);

    return () => clearInterval(timer);
  }, [onComplete]);

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      <div className="text-center">
        <div className="loading-spinner mx-auto mb-8"></div>
        <h2 className="text-2xl font-light metallic-gradient mb-4">WINASTRA</h2>
        <div className="w-64 h-1 bg-gray-800 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-gray-400 to-yellow-500 transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}></div>
        </div>
        <p className="text-gray-400 text-sm mt-4 mono-font">{progress}% LOADING</p>
      </div>
    </div>
  );
};

export default LoadingScreen;
