import { Instagram, Linkedin, Mail, MapPin, Phone, Twitter, Youtube } from 'lucide-react';
import React from 'react';
import { useUnifiedContent } from '../hooks/useUnifiedContent';

const Footer: React.FC = () => {
  const { company, contact, sections, ui } = useUnifiedContent();

  return (
    <footer className="bg-black text-white py-8 border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="lg:col-span-2">
            <h3 className="text-3xl font-bold metallic-gradient mb-6">{company.name}</h3>
            <p className="text-gray-400 leading-relaxed mb-6 max-w-md">
              {sections.footer.description}
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="w-12 h-12 rounded-full glass-effect flex items-center justify-center hover:bg-white/10 transition-colors duration-200 group">
                <Instagram
                  size={20}
                  className="group-hover:text-yellow-500 transition-colors duration-200"
                />
              </a>
              <a
                href="#"
                className="w-12 h-12 rounded-full glass-effect flex items-center justify-center hover:bg-white/10 transition-colors duration-200 group">
                <Linkedin
                  size={20}
                  className="group-hover:text-yellow-500 transition-colors duration-200"
                />
              </a>
              <a
                href="#"
                className="w-12 h-12 rounded-full glass-effect flex items-center justify-center hover:bg-white/10 transition-colors duration-200 group">
                <Twitter
                  size={20}
                  className="group-hover:text-yellow-500 transition-colors duration-200"
                />
              </a>
              <a
                href="#"
                className="w-12 h-12 rounded-full glass-effect flex items-center justify-center hover:bg-white/10 transition-colors duration-200 group">
                <Youtube
                  size={20}
                  className="group-hover:text-yellow-500 transition-colors duration-200"
                />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">{ui.footer.services}</h4>
            <ul className="space-y-3">
              {[
                'Architectural Systems',
                'Aerospace Components',
                'Marine Applications',
                'Industrial Solutions',
                'Custom Fabrication',
                'Surface Treatments',
              ].map(link => (
                <li key={link}>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">{ui.footer.contact}</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin size={16} className="text-gray-400 mt-1 flex-shrink-0" />
                <div className="text-sm text-gray-400">
                  <div>{contact.address.street}</div>
                  <div>{contact.address.city}, {contact.address.state} {contact.address.zip}</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone size={16} className="text-gray-400 flex-shrink-0" />
                <span className="text-sm text-gray-400">{contact.phone}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail size={16} className="text-gray-400 flex-shrink-0" />
                <span className="text-sm text-gray-400">{contact.email}</span>
              </div>
            </div>

            <div className="mt-6 p-4 glass-effect rounded-lg">
              <h5 className="text-sm font-semibold text-white mb-2">Quality Certifications</h5>
              <div className="text-xs text-gray-400 space-y-1">
                <div>ISO 9001:2015 Certified</div>
                <div>AS9100D Aerospace Standard</div>
                <div>IATF 16949 Automotive</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 text-sm text-gray-400">
              <p>{sections.footer.copyright}</p>
              <div className="flex space-x-4">
                <a href="#" className="hover:text-white transition-colors duration-200">
                  {ui.footer.privacyPolicy}
                </a>
                <a href="#" className="hover:text-white transition-colors duration-200">
                  {ui.footer.termsOfService}
                </a>
                <a href="#" className="hover:text-white transition-colors duration-200">
                  {ui.footer.sustainability}
                </a>
              </div>
            </div>
            <div className="text-sm text-gray-400 mono-font">{sections.footer.tagline}</div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
