:root {
  --color-primary-black: #0a0a0a;
  --color-secondary-black: #1a1a1a;
  --color-tertiary-black: #2a2a2a;
  --color-quaternary-black: #3a3a3a;
  --color-metallic-silver: #c0c0c0;
  --color-metallic-gold: #d4af37;
  --color-metallic-bronze: #cd7f32;
  --color-metallic-platinum: #e5e4e2;
  --color-metallic-copper: #b87333;
  --color-accent-blue: #0066cc;
  --color-accent-green: #00cc66;
  --color-accent-red: #cc0066;
  --color-accent-purple: #6b46c1;
  --color-accent-orange: #ea580c;
  --color-text-primary: #ffffff;
  --color-text-secondary: #e5e5e5;
  --color-text-tertiary: #d1d5db;
  --color-text-muted: #a0a0a0;
  --color-text-disabled: #666666;
  --color-text-inverse: #000000;
  --color-surface-primary: rgba(255, 255, 255, 0.05);
  --color-surface-secondary: rgba(255, 255, 255, 0.1);
  --color-surface-tertiary: rgba(255, 255, 255, 0.15);
  --color-surface-quaternary: rgba(255, 255, 255, 0.2);
  --color-glass-light: rgba(255, 255, 255, 0.08);
  --color-glass-medium: rgba(255, 255, 255, 0.12);
  --color-glass-strong: rgba(255, 255, 255, 0.16);
  --font-family-primary: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-display: "Inter", serif;
  --font-family-mono: "JetBrains Mono", "Fira Code", monospace;
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --font-size-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --font-size-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --font-size-5xl: clamp(3rem, 2.5rem + 2.5vw, 3.75rem);
  --font-size-6xl: clamp(3.75rem, 3rem + 3.75vw, 4.5rem);
  --font-size-7xl: clamp(4.5rem, 3.5rem + 5vw, 6rem);
  --font-size-8xl: clamp(6rem, 4.5rem + 7.5vw, 8rem);
  --font-size-9xl: clamp(8rem, 6rem + 10vw, 10rem);
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  --line-height-none: 1;
  --line-height-tight: 1.2;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  --space-px: 1px;
  --space-0: 0;
  --space-0-5: 0.125rem;
  --space-1: 0.25rem;
  --space-1-5: 0.375rem;
  --space-2: 0.5rem;
  --space-2-5: 0.625rem;
  --space-3: 0.75rem;
  --space-3-5: 0.875rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-7: 1.75rem;
  --space-8: 2rem;
  --space-9: 2.25rem;
  --space-10: 2.5rem;
  --space-11: 2.75rem;
  --space-12: 3rem;
  --space-14: 3.5rem;
  --space-16: 4rem;
  --space-18: 4.5rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-28: 7rem;
  --space-32: 8rem;
  --space-36: 9rem;
  --space-40: 10rem;
  --space-44: 11rem;
  --space-48: 12rem;
  --space-52: 13rem;
  --space-56: 14rem;
  --space-60: 15rem;
  --space-64: 16rem;
  --space-72: 18rem;
  --space-80: 20rem;
  --space-96: 24rem;

  /* Optimized spacing scale for better visual hierarchy */
  --section-spacing-xs: 2rem; /* 32px */
  --section-spacing-sm: 3rem; /* 48px */
  --section-spacing-md: 4rem; /* 64px */
  --section-spacing-lg: 5rem; /* 80px */
  --section-spacing-xl: 6rem; /* 96px */
  --section-spacing-2xl: 8rem; /* 128px */

  --content-spacing-xs: 1rem; /* 16px */
  --content-spacing-sm: 1.5rem; /* 24px */
  --content-spacing-md: 2rem; /* 32px */
  --content-spacing-lg: 3rem; /* 48px */
  --content-spacing-xl: 4rem; /* 64px */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;
  --radius-full: 9999px;
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 50px 100px -20px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --glow-sm: 0 0 5px rgba(212, 175, 55, 0.3);
  --glow-base: 0 0 10px rgba(212, 175, 55, 0.4);
  --glow-lg: 0 0 20px rgba(212, 175, 55, 0.5);
  --glow-xl: 0 0 40px rgba(212, 175, 55, 0.6);
  --transition-none: 0s;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slower: 700ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slowest: 1000ms cubic-bezier(0.4, 0, 0.2, 1);
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}
.btn-primary {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver),
    #e8e8e8,
    var(--color-metallic-silver)
  );
  color: var(--color-primary-black);
}
.btn-secondary {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-surface-secondary);
}
.btn-ghost {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid transparent;
}
.card {
  background: var(--color-surface-primary);
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-surface-secondary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
}
.glass-effect {
  background: var(--color-surface-primary);
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-surface-secondary);
}
.metallic-gradient {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver) 0%,
    #e8e8e8 25%,
    var(--color-metallic-silver) 50%,
    #a8a8a8 75%,
    var(--color-metallic-silver) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.metallic-button {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver) 0%,
    #e8e8e8 25%,
    var(--color-metallic-silver) 50%,
    #a8a8a8 75%,
    var(--color-metallic-silver) 100%
  );
  color: var(--color-primary-black);
  border: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
}
.metallic-button:hover {
  background: linear-gradient(
    135deg,
    #d4af37 0%,
    #f4d03f 25%,
    #d4af37 50%,
    #b7950b 75%,
    #d4af37 100%
  );
}
.text-gradient {
  background: linear-gradient(135deg, var(--color-metallic-silver), var(--color-metallic-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.hover-lift {
  transition:
    transform var(--transition-base),
    box-shadow var(--transition-base);
}
.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}
.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}
.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.glass-light {
  background: var(--color-glass-light);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.glass-medium {
  background: var(--color-glass-medium);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.glass-strong {
  background: var(--color-glass-strong);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.gradient-metallic {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver) 0%,
    var(--color-metallic-gold) 50%,
    var(--color-metallic-silver) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.gradient-premium {
  background: linear-gradient(
    135deg,
    var(--color-metallic-platinum) 0%,
    var(--color-metallic-gold) 25%,
    var(--color-metallic-copper) 50%,
    var(--color-metallic-gold) 75%,
    var(--color-metallic-platinum) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.shadow-glow {
  box-shadow: var(--glow-base);
}
.shadow-glow-lg {
  box-shadow: var(--glow-lg);
}
.shadow-premium {
  box-shadow: var(--shadow-xl), var(--glow-sm);
}
.hover-glow {
  transition: all var(--transition-base);
}
.hover-glow:hover {
  box-shadow: var(--glow-lg);
  transform: translateY(-2px);
}
.hover-scale {
  transition: transform var(--transition-base);
}
.hover-scale:hover {
  transform: scale(1.05);
}
.hover-scale-sm:hover {
  transform: scale(1.02);
}
.hover-scale-lg:hover {
  transform: scale(1.1);
}
.text-premium {
  font-family: var(--font-family-display);
  font-weight: var(--font-weight-light);
  letter-spacing: -0.025em;
}
.text-display {
  font-family: var(--font-family-display);
  font-weight: var(--font-weight-extralight);
  letter-spacing: -0.05em;
  line-height: var(--line-height-none);
}
.text-mono {
  font-family: var(--font-family-mono);
  font-variant-numeric: tabular-nums;
}
.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}
@keyframes glow {
  from {
    box-shadow: var(--glow-sm);
  }
  to {
    box-shadow: var(--glow-lg);
  }
}
.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
/* Optimized spacing utility classes */
.section-spacing-xs {
  padding-top: var(--section-spacing-xs);
  padding-bottom: var(--section-spacing-xs);
}
.section-spacing-sm {
  padding-top: var(--section-spacing-sm);
  padding-bottom: var(--section-spacing-sm);
}
.section-spacing-md {
  padding-top: var(--section-spacing-md);
  padding-bottom: var(--section-spacing-md);
}
.section-spacing-lg {
  padding-top: var(--section-spacing-lg);
  padding-bottom: var(--section-spacing-lg);
}
.section-spacing-xl {
  padding-top: var(--section-spacing-xl);
  padding-bottom: var(--section-spacing-xl);
}
.section-spacing-2xl {
  padding-top: var(--section-spacing-2xl);
  padding-bottom: var(--section-spacing-2xl);
}

.content-spacing-xs {
  margin-bottom: var(--content-spacing-xs);
}
.content-spacing-sm {
  margin-bottom: var(--content-spacing-sm);
}
.content-spacing-md {
  margin-bottom: var(--content-spacing-md);
}
.content-spacing-lg {
  margin-bottom: var(--content-spacing-lg);
}
.content-spacing-xl {
  margin-bottom: var(--content-spacing-xl);
}

/* Responsive spacing adjustments */
@media (max-width: 768px) {
  :root {
    --font-size-5xl: 2.5rem;
    --font-size-6xl: 3rem;
    --font-size-7xl: 3.5rem;
    --font-size-8xl: 4rem;

    /* Reduce spacing on mobile */
    --section-spacing-xs: 1.5rem;
    --section-spacing-sm: 2rem;
    --section-spacing-md: 2.5rem;
    --section-spacing-lg: 3rem;
    --section-spacing-xl: 4rem;
    --section-spacing-2xl: 5rem;
  }
  .text-display {
    letter-spacing: -0.025em;
  }
}
