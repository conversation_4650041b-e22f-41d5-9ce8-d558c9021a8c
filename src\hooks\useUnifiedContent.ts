import { useMemo } from 'react';
import {
  APP_CONFIG,
  CERTIFICATIONS,
  COMPANY_INFO,
  COMPANY_STATS,
  CONTACT_INFO,
  CTA_MESSAGES,
  HERO_CONTENT,
  HERO_SPECS,
  MANUFACTURING_CAPABILITIES,
  MEDIA_ASSETS,
  NAVIGATION,
  PRODUCT_CATEGORIES,
  SECTION_CONTENT,
  SEO_CONTENT,
  TECHNICAL_SPECS,
  UI_TEXT,
  type CompanyInfo,
  type ContactInfo,
  type NavigationItem,
} from '../content/unified';

interface UseUnifiedContentReturn {
  // Core company data
  company: CompanyInfo;
  contact: ContactInfo;
  navigation: NavigationItem[];
  
  // Content sections
  hero: typeof HERO_CONTENT;
  sections: typeof SECTION_CONTENT;
  cta: typeof CTA_MESSAGES;
  ui: typeof UI_TEXT;
  
  // Data and specifications
  heroSpecs: typeof HERO_SPECS;
  capabilities: typeof MANUFACTURING_CAPABILITIES;
  certifications: typeof CERTIFICATIONS;
  stats: typeof COMPANY_STATS;
  products: typeof PRODUCT_CATEGORIES;
  specs: typeof TECHNICAL_SPECS;
  
  // Media and SEO
  media: typeof MEDIA_ASSETS;
  seo: typeof SEO_CONTENT;
  
  // App configuration
  config: typeof APP_CONFIG;
  
  // Helper functions
  getHeroContent: (page: string) => typeof HERO_CONTENT.home | null;
  getSEOContent: (page: string) => typeof SEO_CONTENT.home | null;
  getMediaAsset: (type: keyof typeof MEDIA_ASSETS, key: string) => string;
  getUIText: (section: keyof typeof UI_TEXT, key?: string) => string | object;
  getCTA: (type: keyof typeof CTA_MESSAGES) => string;
}

export const useUnifiedContent = (): UseUnifiedContentReturn => {
  const content = useMemo(
    () => ({
      company: COMPANY_INFO,
      contact: CONTACT_INFO,
      navigation: NAVIGATION,
      hero: HERO_CONTENT,
      sections: SECTION_CONTENT,
      cta: CTA_MESSAGES,
      ui: UI_TEXT,
      heroSpecs: HERO_SPECS,
      capabilities: MANUFACTURING_CAPABILITIES,
      certifications: CERTIFICATIONS,
      stats: COMPANY_STATS,
      products: PRODUCT_CATEGORIES,
      specs: TECHNICAL_SPECS,
      media: MEDIA_ASSETS,
      seo: SEO_CONTENT,
      config: APP_CONFIG,
    }),
    []
  );

  const getHeroContent = (page: string) => {
    const heroKey = page as keyof typeof HERO_CONTENT;
    return HERO_CONTENT[heroKey] || HERO_CONTENT.home;
  };

  const getSEOContent = (page: string) => {
    const seoKey = page as keyof typeof SEO_CONTENT;
    return SEO_CONTENT[seoKey] || SEO_CONTENT.home;
  };

  const getMediaAsset = (type: keyof typeof MEDIA_ASSETS, key: string): string => {
    const mediaType = MEDIA_ASSETS[type];
    if (mediaType && typeof mediaType === 'object' && key in mediaType) {
      return (mediaType as any)[key];
    }
    return '';
  };

  const getUIText = (section: keyof typeof UI_TEXT, key?: string): string | object => {
    const uiSection = UI_TEXT[section];
    if (key && typeof uiSection === 'object' && key in uiSection) {
      return (uiSection as any)[key];
    }
    return uiSection;
  };

  const getCTA = (type: keyof typeof CTA_MESSAGES): string => {
    return CTA_MESSAGES[type];
  };

  return {
    ...content,
    getHeroContent,
    getSEOContent,
    getMediaAsset,
    getUIText,
    getCTA,
  };
};

// Hook for navigation with active state
export const useNavigation = (currentPath: string = '/') => {
  const { navigation } = useUnifiedContent();

  return useMemo(
    () =>
      navigation.map(item => ({
        ...item,
        isActive:
          item.href === currentPath || (item.href !== '/' && currentPath.startsWith(item.href)),
      })),
    [navigation, currentPath]
  );
};

// Hook for company branding
export const useBranding = () => {
  const { company } = useUnifiedContent();

  return useMemo(
    () => ({
      name: company.name,
      tagline: company.tagline,
      description: company.description,
      fullName: `${company.name} - ${company.tagline}`,
      shortDescription: company.description.split('.')[0] + '.',
    }),
    [company]
  );
};

// Hook for theme-aware UI text
export const useUIText = () => {
  const { ui, getCTA } = useUnifiedContent();
  
  return useMemo(
    () => ({
      ...ui,
      getCTA,
    }),
    [ui, getCTA]
  );
};

export default useUnifiedContent;
