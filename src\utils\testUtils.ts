// Test utilities for quality assurance

export const testAccessibility = () => {
  const results: string[] = [];

  // Check for skip links
  const skipLinks = document.querySelectorAll('a[href="#main-content"]');
  if (skipLinks.length === 0) {
    results.push('❌ Missing skip links for accessibility');
  } else {
    results.push('✅ Skip links present');
  }

  // Check for alt text on images
  const images = document.querySelectorAll('img');
  let missingAlt = 0;
  images.forEach(img => {
    if (!img.alt && !img.getAttribute('aria-hidden')) {
      missingAlt++;
    }
  });

  if (missingAlt > 0) {
    results.push(`❌ ${missingAlt} images missing alt text`);
  } else {
    results.push('✅ All images have alt text');
  }

  // Check for proper heading hierarchy
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let headingIssues = 0;
  let lastLevel = 0;

  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > lastLevel + 1) {
      headingIssues++;
    }
    lastLevel = level;
  });

  if (headingIssues > 0) {
    results.push(`❌ ${headingIssues} heading hierarchy issues`);
  } else {
    results.push('✅ Proper heading hierarchy');
  }

  // Check for ARIA labels on interactive elements
  const buttons = document.querySelectorAll('button');
  let missingLabels = 0;
  buttons.forEach(button => {
    if (
      !button.textContent?.trim() &&
      !button.getAttribute('aria-label') &&
      !button.getAttribute('aria-labelledby')
    ) {
      missingLabels++;
    }
  });

  if (missingLabels > 0) {
    results.push(`❌ ${missingLabels} buttons missing accessible labels`);
  } else {
    results.push('✅ All buttons have accessible labels');
  }

  return results;
};

export const testPerformance = () => {
  const results: string[] = [];

  // Check if performance API is available
  if (!('performance' in window)) {
    results.push('❌ Performance API not available');
    return results;
  }

  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

  if (navigation) {
    const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
    const domContentLoaded =
      navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;

    if (loadTime < 3000) {
      results.push(`✅ Page load time: ${loadTime}ms (Good)`);
    } else {
      results.push(`⚠️ Page load time: ${loadTime}ms (Could be improved)`);
    }

    if (domContentLoaded < 1500) {
      results.push(`✅ DOM ready time: ${domContentLoaded}ms (Good)`);
    } else {
      results.push(`⚠️ DOM ready time: ${domContentLoaded}ms (Could be improved)`);
    }
  }

  // Check for lazy loading
  const lazyImages = document.querySelectorAll('img[loading="lazy"]');
  if (lazyImages.length > 0) {
    results.push(`✅ ${lazyImages.length} images using lazy loading`);
  } else {
    results.push('⚠️ No lazy loading detected');
  }

  return results;
};

export const testResponsiveness = () => {
  const results: string[] = [];

  // Check viewport meta tag
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    results.push('✅ Viewport meta tag present');
  } else {
    results.push('❌ Missing viewport meta tag');
  }

  // Check for responsive images
  const responsiveImages = document.querySelectorAll('img[srcset], picture');
  if (responsiveImages.length > 0) {
    results.push(`✅ ${responsiveImages.length} responsive images found`);
  } else {
    results.push('⚠️ No responsive images detected');
  }

  // Check for mobile-friendly touch targets
  const buttons = document.querySelectorAll('button, a');
  let smallTargets = 0;

  buttons.forEach(element => {
    const rect = element.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      smallTargets++;
    }
  });

  if (smallTargets === 0) {
    results.push('✅ All touch targets are appropriately sized');
  } else {
    results.push(`⚠️ ${smallTargets} touch targets may be too small`);
  }

  return results;
};

export const testSEO = () => {
  const results: string[] = [];

  // Check title
  const title = document.querySelector('title');
  if (title && title.textContent && title.textContent.length > 10) {
    results.push('✅ Page title present and descriptive');
  } else {
    results.push('❌ Missing or inadequate page title');
  }

  // Check meta description
  const description = document.querySelector('meta[name="description"]');
  if (
    description &&
    description.getAttribute('content') &&
    description.getAttribute('content')!.length > 50
  ) {
    results.push('✅ Meta description present and descriptive');
  } else {
    results.push('❌ Missing or inadequate meta description');
  }

  // Check for Open Graph tags
  const ogTags = document.querySelectorAll('meta[property^="og:"]');
  if (ogTags.length >= 3) {
    results.push('✅ Open Graph tags present');
  } else {
    results.push('❌ Missing Open Graph tags');
  }

  // Check for structured data
  const structuredData = document.querySelector('script[type="application/ld+json"]');
  if (structuredData) {
    results.push('✅ Structured data present');
  } else {
    results.push('❌ Missing structured data');
  }

  return results;
};

export const runAllTests = () => {
  console.group('🧪 Website Quality Assurance Tests');

  console.group('♿ Accessibility Tests');
  testAccessibility().forEach(result => console.log(result));
  console.groupEnd();

  console.group('⚡ Performance Tests');
  testPerformance().forEach(result => console.log(result));
  console.groupEnd();

  console.group('📱 Responsiveness Tests');
  testResponsiveness().forEach(result => console.log(result));
  console.groupEnd();

  console.group('🔍 SEO Tests');
  testSEO().forEach(result => console.log(result));
  console.groupEnd();

  console.groupEnd();

  return {
    accessibility: testAccessibility(),
    performance: testPerformance(),
    responsiveness: testResponsiveness(),
    seo: testSEO(),
  };
};
