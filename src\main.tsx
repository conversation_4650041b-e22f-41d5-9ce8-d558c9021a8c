import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import ErrorBoundary from './components/ErrorBoundary';
import Router from './components/Router';
import './index.css';
import { initializeAccessibility } from './utils/accessibility';
import { measurePerformance } from './utils/performance';
import { initializeSecurity } from './utils/security';
import { runWebsiteTests } from './utils/testSuite';
import { generateDetailedReport } from './utils/websiteAudit';

// Initialize security and accessibility features
initializeSecurity();
initializeAccessibility();

// Performance monitoring and comprehensive audit
if ('performance' in window) {
  window.addEventListener('load', () => {
    const perfData = measurePerformance();
    if (perfData) {
      console.log('Page Load Performance:', perfData);
    }

    // Run comprehensive website audit in development
    if (import.meta.env.DEV) {
      setTimeout(() => {
        // Generate detailed audit report
        generateDetailedReport();

        // Run website tests
        runWebsiteTests();
      }, 2000);
    }
  });
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ErrorBoundary>
      <Router />
    </ErrorBoundary>
  </StrictMode>
);
