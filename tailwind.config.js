/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace'],
        display: ['Inter', 'serif'],
      },
      colors: {
        'primary-black': '#0a0a0a',
        'secondary-black': '#1a1a1a',
        'tertiary-black': '#2a2a2a',
        'metallic-silver': '#c0c0c0',
        'metallic-gold': '#d4af37',
        'metallic-bronze': '#cd7f32',
        luxury: {
          platinum: '#e5e4e2',
          champagne: '#f7e7ce',
          'rose-gold': '#e8b4a0',
          'deep-charcoal': '#0d0d0d',
          midnight: '#1a1a1a',
          'accent-gold': '#c9a96e',
          'accent-copper': '#b87333',
        },
      },
      spacing: {
        18: '4.5rem',
        88: '22rem',
        128: '32rem',
        micro: '0.125rem',
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '3rem',
        '3xl': '4rem',
        '4xl': '6rem',
        '5xl': '8rem',
      },
      borderRadius: {
        '3xl': '2rem',
        '4xl': '2.5rem',
      },
      backdropBlur: {
        xs: '2px',
      },
      boxShadow: {
        'luxury-soft': '0 4px 20px rgba(0, 0, 0, 0.08)',
        'luxury-medium': '0 8px 40px rgba(0, 0, 0, 0.12)',
        'luxury-strong': '0 16px 60px rgba(0, 0, 0, 0.16)',
        'luxury-dramatic': '0 32px 80px rgba(0, 0, 0, 0.24)',
      },
      animation: {
        'fade-in': 'fadeIn 0.8s ease-out forwards',
        'slide-up': 'slideUp 0.8s ease-out forwards',
        float: 'float 6s ease-in-out infinite',
        'luxury-fade-up': 'luxuryFadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards',
        'luxury-slide-left': 'luxurySlideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'luxury-slide-right': 'luxurySlideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'luxury-scale': 'luxuryScaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'luxury-float': 'luxuryFloat 4s ease-in-out infinite',
        'luxury-shimmer': 'luxuryShimmer 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(60px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
      },
      zIndex: {
        60: '60',
        70: '70',
        80: '80',
        90: '90',
        100: '100',
      },
    },
  },
  plugins: [],
};
