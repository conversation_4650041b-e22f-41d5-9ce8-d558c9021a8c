// Type Guards and Utility Functions for Enhanced Type Safety

/**
 * Type guard to check if a value is not null or undefined
 */
export function isNotNullish<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Type guard to check if a value is a string
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * Type guard to check if a value is a number
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * Type guard to check if a value is a boolean
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * Type guard to check if a value is an object (not null, not array)
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Type guard to check if a value is an array
 */
export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value);
}

/**
 * Type guard to check if a value is a function
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

/**
 * Type guard to check if an object has a specific property
 */
export function hasProperty<K extends string>(obj: unknown, prop: K): obj is Record<K, unknown> {
  return isObject(obj) && prop in obj;
}

/**
 * Type guard to check if an object has multiple properties
 */
export function hasProperties<K extends string>(
  obj: unknown,
  props: K[]
): obj is Record<K, unknown> {
  return isObject(obj) && props.every(prop => prop in obj);
}

/**
 * Safe property access with type checking
 */
export function safeGet<T>(obj: unknown, path: string, defaultValue: T): T {
  if (!isObject(obj)) return defaultValue;

  const keys = path.split('.');
  let current: any = obj;

  for (const key of keys) {
    if (!isObject(current) || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }

  return current ?? defaultValue;
}

/**
 * Type-safe array filter that removes null/undefined values
 */
export function filterNullish<T>(array: (T | null | undefined)[]): T[] {
  return array.filter(isNotNullish);
}

/**
 * Type guard for DOM elements
 */
export function isElement(value: unknown): value is Element {
  return value instanceof Element;
}

/**
 * Type guard for HTML elements
 */
export function isHTMLElement(value: unknown): value is HTMLElement {
  return value instanceof HTMLElement;
}

/**
 * Type guard for Error objects
 */
export function isError(value: unknown): value is Error {
  return value instanceof Error;
}

/**
 * Type guard for Promise objects
 */
export function isPromise<T>(value: unknown): value is Promise<T> {
  return (
    value instanceof Promise ||
    (isObject(value) && isFunction((value as any).then) && isFunction((value as any).catch))
  );
}

/**
 * Safe JSON parsing with type checking
 */
export function safeJsonParse<T>(json: string, defaultValue: T): T {
  try {
    const parsed = JSON.parse(json);
    return parsed ?? defaultValue;
  } catch {
    return defaultValue;
  }
}

/**
 * Type-safe environment variable access
 */
export function getEnvVar(key: string, defaultValue: string = ''): string {
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    const value = import.meta.env[key];
    return isString(value) ? value : defaultValue;
  }
  return defaultValue;
}

/**
 * Type guard for valid URLs
 */
export function isValidUrl(value: unknown): value is string {
  if (!isString(value)) return false;

  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Type guard for valid email addresses
 */
export function isValidEmail(value: unknown): value is string {
  if (!isString(value)) return false;

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * Type guard for valid phone numbers (basic)
 */
export function isValidPhone(value: unknown): value is string {
  if (!isString(value)) return false;

  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Assert function for runtime type checking
 */
export function assert(condition: unknown, message: string): asserts condition {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`);
  }
}

/**
 * Type-safe object key checking
 */
export function isValidKey<T extends Record<string, unknown>>(
  obj: T,
  key: string | number | symbol
): key is keyof T {
  return key in obj;
}

/**
 * Safe array access with bounds checking
 */
export function safeArrayAccess<T>(array: T[], index: number, defaultValue: T): T {
  if (!isArray(array) || !isNumber(index)) return defaultValue;
  if (index < 0 || index >= array.length) return defaultValue;
  return array[index] ?? defaultValue;
}

/**
 * Type guard for React components
 */
export function isReactComponent(value: unknown): value is React.ComponentType<any> {
  return isFunction(value) || (isObject(value) && hasProperty(value, 'render'));
}

/**
 * Type-safe localStorage access
 */
export function safeLocalStorage() {
  const isAvailable = (() => {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  })();

  return {
    getItem<T>(key: string, defaultValue: T): T {
      if (!isAvailable) return defaultValue;

      try {
        const item = localStorage.getItem(key);
        return item ? safeJsonParse(item, defaultValue) : defaultValue;
      } catch {
        return defaultValue;
      }
    },

    setItem<T>(key: string, value: T): boolean {
      if (!isAvailable) return false;

      try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch {
        return false;
      }
    },

    removeItem(key: string): boolean {
      if (!isAvailable) return false;

      try {
        localStorage.removeItem(key);
        return true;
      } catch {
        return false;
      }
    },
  };
}

/**
 * Debounce function with proper typing
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function with proper typing
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}
