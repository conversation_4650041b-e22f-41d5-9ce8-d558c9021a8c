import { Award, CheckCircle, FileText, Shield } from 'lucide-react';
import React from 'react';

const QualityPage: React.FC = () => {
  const certifications = [
    {
      icon: Award,
      title: 'ISO 9001:2015',
      description: 'Quality Management Systems',
      details: 'Certified for consistent quality and continuous improvement processes',
      validUntil: '2025',
    },
    {
      icon: Shield,
      title: 'AS9100D',
      description: 'Aerospace Quality Management',
      details: 'Specialized certification for aerospace industry requirements',
      validUntil: '2025',
    },
    {
      icon: CheckCircle,
      title: 'IATF 16949',
      description: 'Automotive Quality Management',
      details: 'Automotive industry quality standards compliance',
      validUntil: '2024',
    },
    {
      icon: FileText,
      title: 'ISO 14001',
      description: 'Environmental Management',
      details: 'Environmental responsibility and sustainability practices',
      validUntil: '2025',
    },
  ];

  const qualityMetrics = [
    {
      metric: '99.97%',
      label: 'First Pass Yield',
      description: 'Products meeting specifications on first inspection',
    },
    { metric: '<0.1%', label: 'Defect Rate', description: 'Industry-leading quality performance' },
    {
      metric: '100%',
      label: 'Traceability',
      description: 'Complete material and process tracking',
    },
    { metric: '24/7', label: 'Quality Monitoring', description: 'Continuous quality surveillance' },
  ];

  const testingCapabilities = [
    {
      category: 'Material Testing',
      tests: [
        'Tensile Strength',
        'Hardness Testing',
        'Chemical Composition',
        'Corrosion Resistance',
      ],
    },
    {
      category: 'Dimensional Inspection',
      tests: ['CMM Measurement', 'Optical Scanning', 'Surface Roughness', 'Geometric Tolerancing'],
    },
    {
      category: 'Performance Validation',
      tests: ['Fatigue Testing', 'Thermal Cycling', 'Environmental Testing', 'Load Testing'],
    },
    {
      category: 'Non-Destructive Testing',
      tests: ['X-Ray Inspection', 'Ultrasonic Testing', 'Dye Penetrant', 'Eddy Current'],
    },
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link sr-only focus:not-sr-only">
        Skip to main content
      </a>

      {/* Hero Section */}
      <section
        id="main-content"
        className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="Quality control laboratory"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">QUALITY</span>
            <br />
            <span className="text-white font-light">ASSURANCE</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Uncompromising quality standards backed by rigorous testing, certification, and
            continuous improvement processes.
          </p>
        </div>
      </section>

      {/* Certifications */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              Certifications & Standards
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Our commitment to excellence is validated by industry-leading certifications and
              adherence to the highest quality standards.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {certifications.map((cert, index) => (
              <div
                key={index}
                className="group hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-effect rounded-2xl p-6 h-full text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 group-hover:scale-110 transition-transform duration-300">
                    <cert.icon className="w-8 h-8 text-black" />
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-2">{cert.title}</h3>

                  <p className="text-gray-400 mb-4 text-sm">{cert.description}</p>

                  <p className="text-gray-300 text-sm leading-relaxed mb-4">{cert.details}</p>

                  <div className="text-xs text-gray-500">Valid until: {cert.validUntil}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Quality Metrics */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Quality Performance</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Measurable excellence through key performance indicators that demonstrate our
              commitment to quality.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {qualityMetrics.map((metric, index) => (
              <div
                key={index}
                className="text-center animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-effect rounded-2xl p-6">
                  <div className="text-4xl font-light metallic-gradient mono-font mb-2">
                    {metric.metric}
                  </div>
                  <div className="text-lg font-semibold text-white mb-2">{metric.label}</div>
                  <div className="text-sm text-gray-400">{metric.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testing Capabilities */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Testing Capabilities</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Comprehensive testing facilities ensuring every product meets the highest standards of
              quality and performance.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {testingCapabilities.map((capability, index) => (
              <div
                key={index}
                className="glass-effect rounded-2xl p-6 animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <h3 className="text-lg font-semibold text-white mb-4">{capability.category}</h3>
                <ul className="space-y-2">
                  {capability.tests.map((test, testIndex) => (
                    <li key={testIndex} className="text-sm text-gray-300 flex items-center">
                      <div className="w-1.5 h-1.5 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full mr-3"></div>
                      {test}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Quality Process */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-light text-white mb-6">Quality Management System</h3>
                <p className="text-gray-400 mb-8 leading-relaxed">
                  Our integrated quality management system ensures consistent excellence throughout
                  every stage of production, from raw material inspection to final product delivery.
                </p>

                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">Incoming material inspection</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">In-process quality control</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">Final inspection and testing</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">Continuous improvement processes</span>
                  </div>
                </div>
              </div>

              <div className="relative">
                <img
                  src="https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="Quality control process"
                  className="w-full h-96 object-cover rounded-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent rounded-2xl"></div>
                <div className="absolute top-6 left-6">
                  <div className="glass-effect rounded-lg p-4">
                    <div className="text-2xl font-light metallic-gradient mono-font">ISO</div>
                    <div className="text-xs text-gray-400 uppercase tracking-wider">Certified</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default QualityPage;
