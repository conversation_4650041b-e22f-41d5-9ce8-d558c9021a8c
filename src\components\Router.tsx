import React, { useEffect, useState } from 'react';
import App from '../App';
import { PAGE_TITLES } from '../constants';
import { initializePageAnimations } from '../hooks/useIntersectionObserver';
import AboutPage from '../pages/AboutPage';
import CareersPage from '../pages/CareersPage';
import NewsPage from '../pages/NewsPage';
import QualityPage from '../pages/QualityPage';
import ServicesPage from '../pages/ServicesPage';
import SustainabilityPage from '../pages/SustainabilityPage';
import CustomCursor from './CustomCursor';
import FloatingNavigation from './FloatingNavigation';

const Router: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('home');

  useEffect(() => {
    // Initialize page from URL
    const path = window.location.pathname.slice(1) || 'home';
    setCurrentPage(path);

    // Handle browser back/forward buttons
    const handlePopState = () => {
      const path = window.location.pathname.slice(1) || 'home';
      setCurrentPage(path);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Initialize scroll animations for all pages
  useEffect(() => {
    // Initialize animations after a short delay to ensure DOM is ready
    const timer = setTimeout(() => {
      const observer = initializePageAnimations();

      // Cleanup function
      return () => {
        observer.disconnect();
      };
    }, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [currentPage]); // Re-run when page changes

  const navigateTo = (page: string) => {
    setCurrentPage(page);
    const url = page === 'home' ? '/' : `/${page}`;
    window.history.pushState({}, '', url);
    document.title = PAGE_TITLES[page as keyof typeof PAGE_TITLES] || PAGE_TITLES.home;
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'about':
        return <AboutPage />;
      case 'services':
        return <ServicesPage />;
      case 'quality':
        return <QualityPage />;
      case 'sustainability':
        return <SustainabilityPage />;
      case 'news':
        return <NewsPage />;
      case 'careers':
        return <CareersPage />;
      default:
        return <App />;
    }
  };

  return (
    <div className="min-h-screen">
      <CustomCursor />
      <FloatingNavigation currentPage={currentPage} onNavigate={navigateTo} />
      {/* <Navigation currentPage={currentPage} onNavigate={navigateTo} /> */}
      <div className="transition-opacity duration-300">{renderPage()}</div>
    </div>
  );
};

export default Router;
