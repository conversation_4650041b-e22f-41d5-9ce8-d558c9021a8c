export interface ImageConfig {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: string;
}
export const PREMIUM_IMAGES = {
  hero: {
    manufacturing:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    precision:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    facility:
      'https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
  },
  manufacturing: {
    cnc: 'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    anodizing:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    quality:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    assembly:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  products: {
    extrusions:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    sheets:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    profiles:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    custom:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    aerospace:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    decorative:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  portfolio: {
    commercial:
      'https://images.pexels.com/photos/1108110/pexels-photo-1108110.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    industrial:
      'https://images.pexels.com/photos/1108111/pexels-photo-1108111.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    marine:
      'https://images.pexels.com/photos/1108112/pexels-photo-1108112.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    architectural:
      'https://images.pexels.com/photos/1108113/pexels-photo-1108113.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  innovation: {
    laboratory:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    research:
      'https://images.pexels.com/photos/1108115/pexels-photo-1108115.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    testing:
      'https://images.pexels.com/photos/1108116/pexels-photo-1108116.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  about: {
    facility:
      'https://images.pexels.com/photos/1108115/pexels-photo-1108115.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    team: 'https://images.pexels.com/photos/1108118/pexels-photo-1108118.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    leadership:
      'https://images.pexels.com/photos/1108119/pexels-photo-1108119.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  services: {
    overview:
      'https://images.pexels.com/photos/1108120/pexels-photo-1108120.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    support:
      'https://images.pexels.com/photos/1108121/pexels-photo-1108121.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  sustainability: {
    green:
      'https://images.pexels.com/photos/1108122/pexels-photo-1108122.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    environment:
      'https://images.pexels.com/photos/1108123/pexels-photo-1108123.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  news: {
    overview:
      'https://images.pexels.com/photos/1108130/pexels-photo-1108130.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    articles: [
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108125/pexels-photo-1108125.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108126/pexels-photo-1108126.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108127/pexels-photo-1108127.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108128/pexels-photo-1108128.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108129/pexels-photo-1108129.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
    ],
  },
  careers: {
    team: 'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    collaboration:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
  quality: {
    control:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    process:
      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
  },
};
export const validateImageUrl = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};
export const optimizeImageUrl = (url: string, options: Partial<ImageConfig> = {}): string => {
  const { width = 800, height = 600, quality = 85 } = options;
  if (url.includes('pexels.com')) {
    const baseUrl = url.split('?')[0];
    return `${baseUrl}?auto=compress&cs=tinysrgb&w=${width}&h=${height}&fit=crop&q=${quality}`;
  }
  return url;
};
export const generatePlaceholder = (width: number = 800, height: number = 600): string => {
  return `data:image/svg+xml;base64,${btoa(` <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg"> <rect width="100%" height="100%" fill="#1a1a1a"/> <rect x="20%" y="40%" width="60%" height="20%" fill="#333" rx="4"/> <text x="50%" y="55%" font-family="Arial, sans-serif" font-size="14" fill="#666" text-anchor="middle">Loading...</text> </svg> `)}`;
};
export const generateAltText = (context: string, description?: string): string => {
  const contextMap: Record<string, string> = {
    hero: 'State-of-the-art aluminum manufacturing facility showcasing precision engineering and advanced CNC machining equipment',
    manufacturing:
      'Advanced aluminum manufacturing process with cutting-edge equipment and quality control systems',
    products: 'High-quality precision aluminum products and components for industrial applications',
    portfolio:
      'Completed aluminum manufacturing project demonstrating engineering excellence and quality craftsmanship',
    innovation:
      'Research and development laboratory for aluminum technology advancement and material testing',
    about:
      'WINASTRA aluminum manufacturing facility with professional team and advanced production capabilities',
    services:
      'Comprehensive aluminum manufacturing services including precision machining and surface treatments',
    sustainability:
      'Sustainable aluminum manufacturing practices and environmental responsibility initiatives',
    news: 'Latest developments and innovations in aluminum manufacturing industry and technology',
    careers:
      'WINASTRA team members collaborating in modern aluminum manufacturing workplace environment',
    quality:
      'Quality control and assurance processes in aluminum manufacturing with precision testing equipment',
  };
  const baseAlt =
    contextMap[context] || 'WINASTRA precision aluminum manufacturing and engineering excellence';
  return description ? `${baseAlt} - ${description}` : baseAlt;
};
export const preloadCriticalImages = (urls: string[]) => {
  urls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    document.head.appendChild(link);
  });
};
export const lazyLoadImages = () => {
  const images = document.querySelectorAll('img[data-src]');
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        img.src = img.dataset.src!;
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  });
  images.forEach(img => imageObserver.observe(img));
};
export const supportsWebP = (): boolean => {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
};
export const supportsAVIF = (): boolean => {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
};
export const generateResponsiveSources = (baseUrl: string, options: Partial<ImageConfig> = {}) => {
  const { quality = 85 } = options;
  const breakpoints = [
    { width: 320, suffix: 'mobile' },
    { width: 768, suffix: 'tablet' },
    { width: 1024, suffix: 'desktop' },
    { width: 1920, suffix: 'large' },
  ];
  return breakpoints.map(bp => ({
    srcSet: optimizeImageUrl(baseUrl, { width: bp.width, quality }),
    media: `(max-width: ${bp.width}px)`,
    type: 'image/webp',
  }));
};
export const getOptimalImageFormat = (url: string): string => {
  if (supportsAVIF()) {
    return url.replace(/\.(jpg|jpeg|png)$/i, '.avif');
  } else if (supportsWebP()) {
    return url.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  }
  return url;
};
export const measureImageLoadTime = (url: string): Promise<number> => {
  return new Promise(resolve => {
    const startTime = performance.now();
    const img = new Image();
    img.onload = () => {
      const loadTime = performance.now() - startTime;
      resolve(loadTime);
    };
    img.onerror = () => {
      resolve(-1);
    };
    img.src = url;
  });
};
export const batchPreloadImages = (
  urls: string[]
): Promise<{ loaded: number; total: number; errors: string[] }> => {
  return new Promise(resolve => {
    let loaded = 0;
    const errors: string[] = [];
    const total = urls.length;
    if (total === 0) {
      resolve({ loaded: 0, total: 0, errors: [] });
      return;
    }
    urls.forEach(url => {
      const img = new Image();
      img.onload = () => {
        loaded++;
        if (loaded + errors.length === total) {
          resolve({ loaded, total, errors });
        }
      };
      img.onerror = () => {
        errors.push(url);
        if (loaded + errors.length === total) {
          resolve({ loaded, total, errors });
        }
      };
      img.src = url;
    });
  });
};
