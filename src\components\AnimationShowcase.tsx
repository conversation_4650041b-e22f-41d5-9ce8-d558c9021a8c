import { Award, Shield, Sparkles, Zap } from 'lucide-react';
import React from 'react';
import MicroInteractions from './MicroInteractions';

const AnimationShowcase: React.FC = () => {
  const features = [
    {
      icon: Sparkles,
      title: 'Premium Animations',
      description: 'Sophisticated micro-interactions that enhance user experience',
      delay: 100,
    },
    {
      icon: Zap,
      title: 'Smooth Transitions',
      description: 'Fluid animations with premium easing functions',
      delay: 200,
    },
    {
      icon: Award,
      title: 'Luxury Effects',
      description: 'High-end visual effects with metallic gradients',
      delay: 300,
    },
    {
      icon: Shield,
      title: 'Accessible Design',
      description: 'Respects user preferences for reduced motion',
      delay: 400,
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/5 rounded-full blur-3xl luxury-animate-float"></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl luxury-animate-float"
          style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <MicroInteractions type="scroll" className="luxury-text-reveal">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6 gradient-premium">
              Premium Interactions
            </h2>
          </MicroInteractions>

          <MicroInteractions type="scroll" delay={200}>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Experience the difference with sophisticated animations and micro-interactions that
              bring our aluminum manufacturing expertise to life.
            </p>
          </MicroInteractions>
        </div>

        {/* Interactive Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <MicroInteractions
              key={index}
              type="hover"
              delay={feature.delay}
              className="luxury-card-hover">
              <div className="glass-medium rounded-2xl p-8 text-center h-full luxury-morph">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 luxury-breathing">
                  <feature.icon className="w-8 h-8 text-black" />
                </div>

                <h3 className="text-xl font-semibold text-white mb-4 luxury-text-reveal">
                  {feature.title}
                </h3>

                <p className="text-gray-400 leading-relaxed">{feature.description}</p>
              </div>
            </MicroInteractions>
          ))}
        </div>

        {/* Interactive Demo Section */}
        <div className="rounded-3xl p-8 md:p-12 bg-white/10 backdrop-blur-xl border border-white/20 shadow-xl">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left side */}
            <div>
              <h3 className="text-3xl font-light text-white mb-6 bg-gradient-to-r from-yellow-400 to-gray-200 bg-clip-text text-transparent">
                Interactive Excellence
              </h3>
              <p className="text-gray-400 mb-8 leading-relaxed">
                Our premium design system includes sophisticated animations and micro-interactions
                that create an engaging and memorable user experience while maintaining
                accessibility standards.
              </p>

              <div className="space-y-6">
                {[
                  'Hover effects with magnetic attraction',
                  'Smooth parallax scrolling elements',
                  'Elastic animations with premium easing',
                  'Glow trails and shimmer effects',
                ].map((item, index) => (
                  <MicroInteractions key={index} type="hover">
                    <div className="flex items-center space-x-4 p-4 rounded-lg bg-white/5 backdrop-blur-md border border-white/10 transition-all duration-500 hover:shadow-[0_0_20px_rgba(255,215,0,0.6)]">
                      <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full animate-pulse"></div>
                      <span className="text-gray-300">{item}</span>
                    </div>
                  </MicroInteractions>
                ))}
              </div>
            </div>

            {/* Right side */}
            <div className="relative space-y-6">
              {/* Click Interaction */}
              <MicroInteractions type="click">
                <div className="rounded-2xl p-6 cursor-pointer bg-white/10 backdrop-blur-lg border border-white/20 shadow-md transform transition-transform hover:scale-[1.02]">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 animate-pulse"></div>
                    <div>
                      <h4 className="text-white font-semibold">Click Me</h4>
                      <p className="text-gray-400 text-sm">Interactive tilt effect</p>
                    </div>
                  </div>
                </div>
              </MicroInteractions>

              {/* Hover Magnetic */}
              <MicroInteractions type="hover">
                <div className="rounded-2xl p-6 bg-white/10 backdrop-blur-lg border border-white/20 shadow-md transition-all duration-300 hover:scale-[1.03]">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 animate-pulse"></div>
                    <div>
                      <h4 className="text-white font-semibold">Hover Me</h4>
                      <p className="text-gray-400 text-sm">Magnetic hover effect</p>
                    </div>
                  </div>
                </div>
              </MicroInteractions>

              {/* Scroll Elastic */}
              <MicroInteractions type="scroll">
                <div className="rounded-2xl p-6 bg-white/10 backdrop-blur-lg border border-white/20 shadow-md transition-transform duration-500">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-400 to-teal-500 animate-[shimmer_2s_infinite]"></div>
                    <div>
                      <h4 className="text-white font-semibold">Scroll Effect</h4>
                      <p className="text-gray-400 text-sm">Elastic reveal animation</p>
                    </div>
                  </div>
                </div>
              </MicroInteractions>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <MicroInteractions type="hover" className="luxury-button-micro">
            <button className="luxury-button-primary px-8 py-4 text-lg font-semibold rounded-xl">
              <span className="flex items-center space-x-3">
                <span>Experience Premium Quality</span>
                <Sparkles className="w-5 h-5" />
              </span>
            </button>
          </MicroInteractions>
        </div>
      </div>
    </section>
  );
};

export default AnimationShowcase;
