import { Award, Shield, Zap } from 'lucide-react';
import React from 'react';
import { useUnifiedContent } from '../hooks/useUnifiedContent';
import { PREMIUM_IMAGES } from '../utils/imageUtils';
import Hero from './Hero';

const HeroSection: React.FC = () => {
  const { hero, heroSpecs, ui } = useUnifiedContent();
  const homeHero = hero.home;

  const iconMap = {
    Award,
    Shield,
    Zap,
  };

  const heroSpecsWithIcons = heroSpecs.map(spec => ({
    ...spec,
    icon: iconMap[spec.icon as keyof typeof iconMap] || Award,
  }));

  return (
    <Hero
      id="home"
      title={homeHero.title}
      subtitle={homeHero.subtitle}
      description={homeHero.description}
      backgroundImage={PREMIUM_IMAGES.hero.manufacturing}
      backgroundAlt={ui.accessibility.heroImageAlt}
      brandMark={homeHero.brandMark}
      specs={heroSpecsWithIcons}
      variant="premium"
    />
  );
};

export default HeroSection;
