import { Award, Shield, Zap } from 'lucide-react';
import React from 'react';
import { useContent } from '../hooks/useContent';
import { PREMIUM_IMAGES } from '../utils/imageUtils';
import Hero from './Hero';

const HeroSection: React.FC = () => {
  const { hero } = useContent();
  const homeHero = hero.home;

  const heroSpecs = [
    {
      icon: Award,
      value: '99.9%',
      label: 'Purity',
      description: 'Material Excellence',
      detail: 'Aerospace-grade aluminum alloys with certified composition',
    },
    {
      icon: Shield,
      value: '±0.01mm',
      label: 'Tolerance',
      description: 'Precision Engineering',
      detail: 'CNC machining accuracy exceeding industry standards',
    },
    {
      icon: Zap,
      value: 'ISO 9001',
      label: 'Certified',
      description: 'Quality Assurance',
      detail: 'International quality management certification',
    },
  ];

  return (
    <Hero
      id="home"
      title={homeHero.title}
      subtitle={homeHero.subtitle}
      description={homeHero.description}
      backgroundImage={PREMIUM_IMAGES.hero.manufacturing}
      backgroundAlt="State-of-the-art aluminum manufacturing facility with precision CNC machining equipment"
      brandMark={homeHero.brandMark}
      specs={heroSpecs}
      variant="premium"
    />
  );
};

export default HeroSection;
