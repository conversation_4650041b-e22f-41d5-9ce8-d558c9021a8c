@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600&display=swap");
@import "./styles/design-system.css";
@import "./styles/premium-enhancements.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
html {
  scroll-behavior: smooth;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body {
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  color: #ffffff;
  background-color: #0a0a0a;
  overflow-x: hidden;
  font-feature-settings:
    "kern" 1,
    "liga" 1;
  text-rendering: optimizeLegibility;
}
*:focus {
  outline: 2px solid #d4af37;
  outline-offset: 2px;
}
*:focus:not(:focus-visible) {
  outline: none;
}
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #d4af37;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(212, 175, 55, 0.2);
  border-radius: 0.25rem;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.skip-link:focus {
  position: absolute;
  top: 6px !important;
  left: 6px;
  background: #d4af37;
  color: #0a0a0a;
  padding: 0.5rem 1rem;
  text-decoration: none;
  z-index: 1000;
  border-radius: 0.25rem;
  font-weight: 500;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid #c0c0c0;
  border-top: 3px solid #d4af37;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: #0a0a0a;
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d4af37, #b8860b);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #b8860b, #d4af37);
}
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  .animate-float {
    animation: none !important;
  }
}
@media (prefers-contrast: high) {
  .glass-effect {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
}
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  .glass-effect {
    display: none !important;
  }
  .metallic-gradient {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }
}
.animate-on-scroll {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}
.grid-pattern {
  background-image:
    linear-gradient(rgba(212, 175, 55, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(212, 175, 55, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}
.mono-font {
  font-family: "JetBrains Mono", "Fira Code", monospace;
}
.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.metallic-gradient {
  background: linear-gradient(
    135deg,
    #c0c0c0 0%,
    #e8e8e8 25%,
    #c0c0c0 50%,
    #a8a8a8 75%,
    #c0c0c0 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.metallic-button {
  background: linear-gradient(
    135deg,
    #c0c0c0 0%,
    #e8e8e8 25%,
    #c0c0c0 50%,
    #a8a8a8 75%,
    #c0c0c0 100%
  );
  color: #0a0a0a;
  border: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.metallic-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}
.metallic-button:hover::before {
  left: 100%;
}
.metallic-button:hover {
  background: linear-gradient(
    135deg,
    #d4af37 0%,
    #f4d03f 25%,
    #d4af37 50%,
    #b7950b 75%,
    #d4af37 100%
  );
}
.hover-lift {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}
.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}
.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}
.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
}
.animate-float {
  animation: float 6s ease-in-out infinite;
}
.hover-lift,
.animate-fade-in,
.animate-float,
.metallic-button {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}
.glass-effect {
  contain: layout style paint;
}
@media (max-width: 768px) {
  .animate-on-scroll {
    transform: translateY(30px);
  }
  .hover-lift:hover {
    transform: translateY(-4px);
  }
  button,
  a,
  input,
  select,
  textarea {
    min-height: 44px;
    min-width: 44px;
  }
}
::selection {
  background: #d4af37;
  color: #0a0a0a;
  text-shadow: none;
}
::-moz-selection {
  background: #d4af37;
  color: #0a0a0a;
  text-shadow: none;
}
::placeholder {
  color: rgba(255, 255, 255, 0.4);
  opacity: 1;
}
::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.4);
}
::-moz-placeholder {
  color: rgba(255, 255, 255, 0.4);
  opacity: 1;
}
:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.4);
}