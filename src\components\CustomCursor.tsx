import React, { useEffect, useRef, useState } from 'react';

interface CursorState {
  x: number;
  y: number;
  isHovering: boolean;
  isClicking: boolean;
  cursorType: 'default' | 'pointer' | 'text' | 'precision' | 'industrial';
}

const CustomCursor: React.FC = () => {
  const cursorRef = useRef<HTMLDivElement>(null);
  const trailRef = useRef<HTMLDivElement>(null);
  const [cursorState, setCursorState] = useState<CursorState>({
    x: 0,
    y: 0,
    isHovering: false,
    isClicking: false,
    cursorType: 'default',
  });

  useEffect(() => {
    const updateCursor = (e: MouseEvent) => {
      setCursorState(prev => ({
        ...prev,
        x: e.clientX,
        y: e.clientY,
      }));
    };

    const handleMouseDown = () => {
      setCursorState(prev => ({ ...prev, isClicking: true }));
    };

    const handleMouseUp = () => {
      setCursorState(prev => ({ ...prev, isClicking: false }));
    };

    const handleMouseEnter = (e: Event) => {
      const target = e.target as HTMLElement;
      let cursorType: CursorState['cursorType'] = 'default';
      
      if (target.matches('button, a, [role="button"]')) {
        cursorType = 'pointer';
      } else if (target.matches('input, textarea, [contenteditable]')) {
        cursorType = 'text';
      } else if (target.matches('.precision-element, .metallic-button, .glass-effect')) {
        cursorType = 'precision';
      } else if (target.matches('.industrial-element, .manufacturing-section')) {
        cursorType = 'industrial';
      }

      setCursorState(prev => ({
        ...prev,
        isHovering: true,
        cursorType,
      }));
    };

    const handleMouseLeave = () => {
      setCursorState(prev => ({
        ...prev,
        isHovering: false,
        cursorType: 'default',
      }));
    };

    // Add event listeners
    document.addEventListener('mousemove', updateCursor);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);
    
    // Add hover listeners to interactive elements
    const interactiveElements = document.querySelectorAll(
      'button, a, input, textarea, [role="button"], [contenteditable], .precision-element, .metallic-button, .glass-effect, .industrial-element, .manufacturing-section'
    );
    
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);
    });

    return () => {
      document.removeEventListener('mousemove', updateCursor);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []);

  // Update cursor position
  useEffect(() => {
    if (cursorRef.current) {
      cursorRef.current.style.transform = `translate(${cursorState.x}px, ${cursorState.y}px)`;
    }
    if (trailRef.current) {
      trailRef.current.style.transform = `translate(${cursorState.x}px, ${cursorState.y}px)`;
    }
  }, [cursorState.x, cursorState.y]);

  const getCursorClasses = () => {
    const baseClasses = 'custom-cursor';
    const stateClasses = [
      cursorState.isHovering && 'hovering',
      cursorState.isClicking && 'clicking',
      `cursor-${cursorState.cursorType}`,
    ].filter(Boolean).join(' ');
    
    return `${baseClasses} ${stateClasses}`;
  };

  return (
    <>
      {/* Cursor trail */}
      <div
        ref={trailRef}
        className="custom-cursor-trail"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 9998,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%)',
          transform: `translate(${cursorState.x}px, ${cursorState.y}px)`,
          transition: 'transform 0.15s ease-out',
        }}
      />
      
      {/* Main cursor */}
      <div
        ref={cursorRef}
        className={getCursorClasses()}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 9999,
          mixBlendMode: 'difference',
        }}
      />
    </>
  );
};

export default CustomCursor;
