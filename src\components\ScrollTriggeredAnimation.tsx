import { useEffect, useRef, useState } from "react";

const ScrollTriggeredAnimation: React.FC<{
    children: React.ReactNode;
    animation?: 'fadeUp' | 'slideLeft' | 'slideRight' | 'scale' | 'stagger';
    delay?: number;
    threshold?: number;
}> = ({
    children,
    animation = 'fadeUp',
    delay = 0,
    threshold = 0.1
}) => {
        const elementRef = useRef<HTMLDivElement>(null);
        const [isVisible, setIsVisible] = useState(false);

        useEffect(() => {
            const element = elementRef.current;
            if (!element) return;

            const observer = new IntersectionObserver(
                ([entry]) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => setIsVisible(true), delay);
                    }
                },
                { threshold }
            );

            observer.observe(element);

            return () => observer.disconnect();
        }, [delay, threshold]);

        return (
            <div
                ref={elementRef}
                className={`scroll-animation ${animation} ${isVisible ? 'visible' : ''}`}
            >
                {children}
            </div>
        );
    };

export default ScrollTriggeredAnimation;
