import { Globe, Lightbulb, Target, Users } from 'lucide-react';
import React from 'react';

const AboutPage: React.FC = () => {
  const milestones = [
    {
      year: '1995',
      title: 'Company Founded',
      description: 'Started as a small aluminum fabrication shop',
    },
    {
      year: '2005',
      title: 'ISO Certification',
      description: 'Achieved ISO 9001:2015 quality certification',
    },
    {
      year: '2010',
      title: 'International Expansion',
      description: 'Expanded operations to serve global markets',
    },
    {
      year: '2015',
      title: 'Innovation Center',
      description: 'Opened state-of-the-art R&D facility',
    },
    {
      year: '2020',
      title: 'Sustainability Initiative',
      description: 'Launched comprehensive green manufacturing program',
    },
    {
      year: '2024',
      title: 'Industry Leadership',
      description: 'Recognized as industry leader in precision aluminum',
    },
  ];

  const values = [
    {
      icon: Target,
      title: 'Precision Excellence',
      description: 'Every component meets the highest standards of accuracy and quality',
    },
    {
      icon: Lightbulb,
      title: 'Innovation Drive',
      description: 'Continuously pushing boundaries in aluminum technology and processes',
    },
    {
      icon: Users,
      title: 'Customer Focus',
      description: 'Building lasting partnerships through exceptional service and solutions',
    },
    {
      icon: Globe,
      title: 'Global Reach',
      description: 'Serving clients worldwide with consistent quality and reliability',
    },
  ];

  const stats = [
    { number: '25+', label: 'Years Experience' },
    { number: '500+', label: 'Projects Completed' },
    { number: '50+', label: 'Countries Served' },
    { number: '99.9%', label: 'Quality Rating' },
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link sr-only focus:not-sr-only">
        Skip to main content
      </a>

      {/* Hero Section */}
      <section
        id="main-content"
        className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="WINASTRA facility"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">CRAFTING</span>
            <br />
            <span className="text-white font-light">EXCELLENCE</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            For over 25 years, WINASTRA has been at the forefront of precision aluminum
            manufacturing, delivering exceptional quality and innovation to clients worldwide.
          </p>
        </div>
      </section>

      {/* Company Story */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-8">Our Story</h2>
              <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                Founded in 1995 with a vision to revolutionize aluminum manufacturing, WINASTRA
                began as a small fabrication shop with big dreams. Our founders believed that
                precision, innovation, and unwavering quality could transform an industry.
              </p>
              <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                Today, we stand as a global leader in aluminum solutions, serving aerospace,
                architectural, marine, and industrial sectors. Our journey from a local workshop to
                an international powerhouse is built on the foundation of excellence and continuous
                innovation.
              </p>
              <p className="text-lg text-gray-300 leading-relaxed">
                Every piece we craft carries our commitment to perfection, backed by cutting-edge
                technology and the expertise of our skilled craftsmen who take pride in delivering
                solutions that exceed expectations.
              </p>
            </div>

            <div className="animate-on-scroll">
              <img
                src="https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="WINASTRA manufacturing process"
                className="w-full h-96 object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Our Journey</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Key milestones that have shaped WINASTRA into the industry leader we are today.
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-gray-400 to-yellow-500"></div>

            {milestones.map((milestone, index) => (
              <div
                key={index}
                className={`relative flex items-center mb-12 animate-on-scroll ${index % 2 === 0 ? 'justify-start' : 'justify-end'
                  }`}
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <div className="glass-effect rounded-2xl p-6">
                    <div className="text-2xl font-light metallic-gradient mono-font mb-2">
                      {milestone.year}
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-2">{milestone.title}</h3>
                    <p className="text-gray-400">{milestone.description}</p>
                  </div>
                </div>

                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full border-4 border-gray-900"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Our Values</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              The principles that guide everything we do and drive our commitment to excellence.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="text-center group animate-on-scroll hover-lift"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-effect rounded-2xl p-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 group-hover:scale-110 transition-transform duration-300">
                    <value.icon className="w-8 h-8 text-black" />
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-4">{value.title}</h3>

                  <p className="text-gray-400 leading-relaxed">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="text-5xl font-light metallic-gradient mono-font mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 uppercase tracking-wider text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Leadership Team</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Meet the visionaries driving WINASTRA's continued innovation and growth.
            </p>
          </div>

          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-light text-white mb-6">Visionary Leadership</h3>
                <p className="text-gray-400 mb-8 leading-relaxed">
                  Our leadership team combines decades of industry experience with a passion for
                  innovation. Together, they guide WINASTRA's strategic vision while maintaining our
                  commitment to quality and customer satisfaction.
                </p>

                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">Strategic industry expertise</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">Innovation-driven mindset</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">Customer-centric approach</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">Sustainable business practices</span>
                  </div>
                </div>
              </div>

              <div className="relative">
                <img
                  src="https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="Leadership team"
                  className="w-full h-96 object-cover rounded-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent rounded-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
