// Comprehensive test suite for WINASTRA website functionality

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  timestamp: Date;
}

class WebsiteTestSuite {
  private results: TestResult[] = [];

  private addResult(name: string, passed: boolean, message: string) {
    this.results.push({
      name,
      passed,
      message,
      timestamp: new Date(),
    });
  }

  // Test navigation functionality
  testNavigation(): boolean {
    try {
      const navigation = document.querySelector('nav');
      if (!navigation) {
        this.addResult('Navigation', false, 'Navigation element not found');
        return false;
      }

      const navLinks = navigation.querySelectorAll('a, button');
      if (navLinks.length === 0) {
        this.addResult('Navigation', false, 'No navigation links found');
        return false;
      }

      this.addResult('Navigation', true, `Found ${navLinks.length} navigation elements`);
      return true;
    } catch (error) {
      this.addResult('Navigation', false, `Error: ${error}`);
      return false;
    }
  }

  // Test content rendering
  testContentRendering(): boolean {
    try {
      const sections = document.querySelectorAll('section');
      if (sections.length === 0) {
        this.addResult('Content Rendering', false, 'No sections found');
        return false;
      }

      let visibleSections = 0;
      sections.forEach(section => {
        const style = window.getComputedStyle(section);
        if (style.display !== 'none' && style.visibility !== 'hidden') {
          visibleSections++;
        }
      });

      if (visibleSections === 0) {
        this.addResult('Content Rendering', false, 'No visible sections found');
        return false;
      }

      this.addResult('Content Rendering', true, `Found ${visibleSections} visible sections`);
      return true;
    } catch (error) {
      this.addResult('Content Rendering', false, `Error: ${error}`);
      return false;
    }
  }

  // Test animations
  testAnimations(): boolean {
    try {
      const animatedElements = document.querySelectorAll('.animate-on-scroll');
      if (animatedElements.length === 0) {
        this.addResult('Animations', false, 'No animated elements found');
        return false;
      }

      this.addResult('Animations', true, `Found ${animatedElements.length} animated elements`);
      return true;
    } catch (error) {
      this.addResult('Animations', false, `Error: ${error}`);
      return false;
    }
  }

  // Test custom cursor
  testCustomCursor(): boolean {
    try {
      const cursor = document.querySelector('.custom-cursor');
      if (!cursor) {
        this.addResult('Custom Cursor', false, 'Custom cursor not found');
        return false;
      }

      const cursorTrail = document.querySelector('.custom-cursor-trail');
      if (!cursorTrail) {
        this.addResult('Custom Cursor', false, 'Cursor trail not found');
        return false;
      }

      this.addResult('Custom Cursor', true, 'Custom cursor system active');
      return true;
    } catch (error) {
      this.addResult('Custom Cursor', false, `Error: ${error}`);
      return false;
    }
  }

  // Test responsive design
  testResponsiveDesign(): boolean {
    try {
      const viewport = window.innerWidth;
      const isMobile = viewport < 768;
      const isTablet = viewport >= 768 && viewport < 1024;
      const isDesktop = viewport >= 1024;

      let deviceType = 'unknown';
      if (isMobile) deviceType = 'mobile';
      else if (isTablet) deviceType = 'tablet';
      else if (isDesktop) deviceType = 'desktop';

      this.addResult('Responsive Design', true, `Viewport: ${viewport}px (${deviceType})`);
      return true;
    } catch (error) {
      this.addResult('Responsive Design', false, `Error: ${error}`);
      return false;
    }
  }

  // Test accessibility
  testAccessibility(): boolean {
    try {
      const skipLink = document.querySelector('.skip-link');
      const ariaLabels = document.querySelectorAll('[aria-label]');
      const altTexts = document.querySelectorAll('img[alt]');

      let score = 0;
      let maxScore = 3;

      if (skipLink) score++;
      if (ariaLabels.length > 0) score++;
      if (altTexts.length > 0) score++;

      const passed = score >= 2;
      this.addResult('Accessibility', passed, `Accessibility score: ${score}/${maxScore}`);
      return passed;
    } catch (error) {
      this.addResult('Accessibility', false, `Error: ${error}`);
      return false;
    }
  }

  // Test performance
  testPerformance(): boolean {
    try {
      if (!('performance' in window)) {
        this.addResult('Performance', false, 'Performance API not available');
        return false;
      }

      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
      const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;

      const passed = loadTime < 3000; // Less than 3 seconds
      this.addResult('Performance', passed, `Load time: ${loadTime}ms, DOM ready: ${domContentLoaded}ms`);
      return passed;
    } catch (error) {
      this.addResult('Performance', false, `Error: ${error}`);
      return false;
    }
  }

  // Run all tests
  runAllTests(): TestResult[] {
    console.log('🧪 Running WINASTRA Website Test Suite...');
    
    this.testNavigation();
    this.testContentRendering();
    this.testAnimations();
    this.testCustomCursor();
    this.testResponsiveDesign();
    this.testAccessibility();
    this.testPerformance();

    return this.results;
  }

  // Generate test report
  generateReport(): string {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);

    let report = `\n🏭 WINASTRA Website Test Report\n`;
    report += `${'='.repeat(40)}\n`;
    report += `Overall Score: ${passed}/${total} (${percentage}%)\n\n`;

    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      report += `${status} ${result.name}: ${result.message}\n`;
    });

    report += `\n${'='.repeat(40)}\n`;
    report += `Test completed at: ${new Date().toLocaleString()}\n`;

    return report;
  }
}

// Export test suite
export const runWebsiteTests = () => {
  const testSuite = new WebsiteTestSuite();
  const results = testSuite.runAllTests();
  const report = testSuite.generateReport();
  
  console.log(report);
  return { results, report };
};

export default WebsiteTestSuite;
