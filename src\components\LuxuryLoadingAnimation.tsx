import React, { useEffect, useState } from 'react';

interface LuxuryLoadingProps {
  type?: 'spinner' | 'pulse' | 'wave' | 'metallic' | 'industrial';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'gold' | 'silver' | 'copper' | 'platinum';
  text?: string;
  showProgress?: boolean;
  progress?: number;
}

const LuxuryLoadingAnimation: React.FC<LuxuryLoadingProps> = ({
  type = 'metallic',
  size = 'md',
  color = 'gold',
  text = 'Loading...',
  showProgress = false,
  progress = 0,
}) => {
  const [animationPhase, setAnimationPhase] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 500);

    return () => clearInterval(interval);
  }, []);

  const getSizeClasses = () => {
    const sizeMap = {
      sm: 'w-8 h-8',
      md: 'w-12 h-12',
      lg: 'w-16 h-16',
      xl: 'w-24 h-24',
    };
    return sizeMap[size];
  };

  const getColorClasses = () => {
    const colorMap = {
      gold: 'luxury-loading-gold',
      silver: 'luxury-loading-silver',
      copper: 'luxury-loading-copper',
      platinum: 'luxury-loading-platinum',
    };
    return colorMap[color];
  };

  const renderSpinner = () => (
    <div className={`luxury-spinner ${getSizeClasses()} ${getColorClasses()}`}>
      <div className="luxury-spinner-inner"></div>
    </div>
  );

  const renderPulse = () => (
    <div className={`luxury-pulse ${getSizeClasses()} ${getColorClasses()}`}>
      <div className="luxury-pulse-ring"></div>
      <div className="luxury-pulse-ring"></div>
      <div className="luxury-pulse-ring"></div>
    </div>
  );

  const renderWave = () => (
    <div className={`luxury-wave ${getColorClasses()}`}>
      {[...Array(5)].map((_, i) => (
        <div
          key={i}
          className="luxury-wave-bar"
          style={{ animationDelay: `${i * 0.1}s` }}
        ></div>
      ))}
    </div>
  );

  const renderMetallic = () => (
    <div className={`luxury-metallic ${getSizeClasses()} ${getColorClasses()}`}>
      <div className="luxury-metallic-gear">
        <div className="luxury-metallic-gear-inner"></div>
      </div>
      <div className="luxury-metallic-particles">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="luxury-metallic-particle"
            style={{
              transform: `rotate(${i * 45}deg) translateY(-20px)`,
              animationDelay: `${i * 0.125}s`,
            }}
          ></div>
        ))}
      </div>
    </div>
  );

  const renderIndustrial = () => (
    <div className={`luxury-industrial ${getSizeClasses()} ${getColorClasses()}`}>
      <div className="luxury-industrial-frame">
        <div className="luxury-industrial-bolt luxury-industrial-bolt-1"></div>
        <div className="luxury-industrial-bolt luxury-industrial-bolt-2"></div>
        <div className="luxury-industrial-bolt luxury-industrial-bolt-3"></div>
        <div className="luxury-industrial-bolt luxury-industrial-bolt-4"></div>
        <div className="luxury-industrial-center">
          <div className="luxury-industrial-logo">W</div>
        </div>
      </div>
    </div>
  );

  const renderLoader = () => {
    switch (type) {
      case 'spinner':
        return renderSpinner();
      case 'pulse':
        return renderPulse();
      case 'wave':
        return renderWave();
      case 'metallic':
        return renderMetallic();
      case 'industrial':
        return renderIndustrial();
      default:
        return renderMetallic();
    }
  };

  return (
    <div className="luxury-loading-container">
      <div className="luxury-loading-animation">
        {renderLoader()}
      </div>
      
      {text && (
        <div className="luxury-loading-text">
          <span className="luxury-loading-text-content">{text}</span>
          <div className="luxury-loading-dots">
            {[...Array(3)].map((_, i) => (
              <span
                key={i}
                className={`luxury-loading-dot ${animationPhase === i ? 'active' : ''}`}
              ></span>
            ))}
          </div>
        </div>
      )}
      
      {showProgress && (
        <div className="luxury-progress-container">
          <div className="luxury-progress-bar">
            <div
              className="luxury-progress-fill"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            ></div>
          </div>
          <div className="luxury-progress-text">
            {Math.round(progress)}%
          </div>
        </div>
      )}
    </div>
  );
};

// Skeleton loading component for content
export const LuxurySkeleton: React.FC<{
  lines?: number;
  height?: string;
  className?: string;
}> = ({ lines = 3, height = '1rem', className = '' }) => (
  <div className={`luxury-skeleton-container ${className}`}>
    {[...Array(lines)].map((_, i) => (
      <div
        key={i}
        className="luxury-skeleton-line"
        style={{
          height,
          width: i === lines - 1 ? '75%' : '100%',
          animationDelay: `${i * 0.1}s`,
        }}
      ></div>
    ))}
  </div>
);

export default LuxuryLoadingAnimation;
