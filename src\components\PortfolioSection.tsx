import { Calendar, ExternalLink, MapPin } from 'lucide-react';
import React, { useState } from 'react';

const PortfolioSection: React.FC = () => {
  const [activeProject, setActiveProject] = useState(0);

  const projects = [
    {
      title: 'Skyline Tower Complex',
      category: 'Commercial',
      location: 'New York, USA',
      year: '2023',
      image:
        'https://images.pexels.com/photos/1108110/pexels-photo-1108110.jpeg?auto=compress&cs=tinysrgb&w=800',
      description: 'Premium aluminum curtain wall system for a 50-story commercial tower',
      details:
        'Delivered over 15,000 square meters of custom aluminum extrusions with precision tolerances for this landmark project.',
      specs: ['6061-T6 Alloy', 'Anodized Finish', 'Thermal Break Technology'],
    },
    {
      title: 'Aerospace Manufacturing Hub',
      category: 'Industrial',
      location: 'Seattle, USA',
      year: '2023',
      image:
        'https://images.pexels.com/photos/1108111/pexels-photo-1108111.jpeg?auto=compress&cs=tinysrgb&w=800',
      description: 'Critical aluminum components for aerospace manufacturing facility',
      details:
        'Supplied high-precision aluminum components meeting stringent aerospace standards and certifications.',
      specs: ['7075-T6 Alloy', 'Aerospace Grade', 'AS9100 Certified'],
    },
    {
      title: 'Luxury Yacht Project',
      category: 'Marine',
      location: 'Monaco',
      year: '2022',
      image:
        'https://images.pexels.com/photos/1108112/pexels-photo-1108112.jpeg?auto=compress&cs=tinysrgb&w=800',
      description: 'Marine-grade aluminum structures for luxury superyacht',
      details:
        'Custom fabricated aluminum components designed to withstand harsh marine environments while maintaining aesthetic appeal.',
      specs: ['5083-H116 Alloy', 'Marine Grade', 'Corrosion Resistant'],
    },
    {
      title: 'Architectural Landmark',
      category: 'Architectural',
      location: 'Dubai, UAE',
      year: '2022',
      image:
        'https://images.pexels.com/photos/1108113/pexels-photo-1108113.jpeg?auto=compress&cs=tinysrgb&w=800',
      description: 'Iconic aluminum facade for prestigious architectural project',
      details:
        'Engineered complex geometric aluminum panels that create a stunning visual impact while providing superior performance.',
      specs: ['6063-T6 Alloy', 'Custom Profiles', 'PVDF Coating'],
    },
  ];

  return (
    <section id="portfolio" className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            Project
            <span className="block metallic-gradient font-light">Portfolio</span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Explore our portfolio of exceptional projects where precision engineering meets
            innovative design solutions.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Project Navigation */}
          <div className="space-y-6 animate-on-scroll">
            {projects.map((project, index) => (
              <div
                key={index}
                className={`cursor-pointer transition-all duration-300 ${
                  activeProject === index
                    ? 'glass-effect rounded-2xl p-6'
                    : 'p-6 hover:bg-white/5 rounded-2xl'
                }`}
                onClick={() => setActiveProject(index)}>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        activeProject === index
                          ? 'bg-gradient-to-r from-gray-400 to-yellow-500'
                          : 'bg-gray-800'
                      }`}>
                      <span className="text-white font-semibold">{index + 1}</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3
                      className={`text-xl font-semibold mb-2 ${
                        activeProject === index ? 'text-white' : 'text-gray-400'
                      }`}>
                      {project.title}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                      <span className="flex items-center space-x-1">
                        <MapPin size={14} />
                        <span>{project.location}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Calendar size={14} />
                        <span>{project.year}</span>
                      </span>
                    </div>
                    <p
                      className={`text-sm leading-relaxed ${
                        activeProject === index ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                      {project.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Project Details */}
          <div className="animate-on-scroll">
            <div className="relative overflow-hidden rounded-3xl">
              <img
                src={projects[activeProject].image}
                alt={`${projects[activeProject].title} - Aluminum manufacturing project`}
                className="w-full h-96 object-cover"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
              <div className="absolute bottom-6 left-6 right-6">
                <span className="inline-block px-3 py-1 bg-white/10 backdrop-blur-md rounded-full text-xs text-white mb-3">
                  {projects[activeProject].category}
                </span>
                <h3 className="text-2xl font-semibold text-white mb-2">
                  {projects[activeProject].title}
                </h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {projects[activeProject].details}
                </p>
              </div>
            </div>

            <div className="mt-6 glass-effect rounded-2xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4">Technical Specifications</h4>
              <div className="flex flex-wrap gap-2 mb-6">
                {projects[activeProject].specs.map((spec, index) => (
                  <span
                    key={index}
                    className="px-3 py-2 bg-gray-800 rounded-lg text-sm text-gray-300">
                    {spec}
                  </span>
                ))}
              </div>
              <button className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                <span className="text-sm">View Case Study</span>
                <ExternalLink
                  size={16}
                  className="group-hover:translate-x-1 transition-transform duration-300"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
