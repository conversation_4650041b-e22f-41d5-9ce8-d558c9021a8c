import { useMemo } from 'react';
import {
  COMPANY_INFO,
  COMPANY_STATS,
  COMPANY_TIMELINE,
  CONTACT_INFO,
  CTA_MESSAGES,
  HERO_CONTENT,
  MEDIA_ASSETS,
  NAVIGATION,
  PAGE_CONTENT,
  PRODUCT_CATEGORIES,
  SEO_CONTENT,
  TECHNICAL_SPECS,
  type CompanyInfo,
  type ContactInfo,
  type ContentSection,
  type NavigationItem,
} from '../content';

interface UseContentReturn {
  company: CompanyInfo;
  contact: ContactInfo;
  navigation: NavigationItem[];
  hero: typeof HERO_CONTENT;
  pages: typeof PAGE_CONTENT;
  products: typeof PRODUCT_CATEGORIES;
  specs: typeof TECHNICAL_SPECS;
  cta: typeof CTA_MESSAGES;
  media: typeof MEDIA_ASSETS;
  stats: typeof COMPANY_STATS;
  timeline: typeof COMPANY_TIMELINE;
  seo: typeof SEO_CONTENT;
  getPageContent: (page: string) => ContentSection | null;
  getHeroContent: (page: string) => typeof HERO_CONTENT.home | null;
  getSEOContent: (page: string) => typeof SEO_CONTENT.home | null;
  getMediaAsset: (type: keyof typeof MEDIA_ASSETS, key: string) => string;
}

export const useContent = (): UseContentReturn => {
  const content = useMemo(
    () => ({
      company: COMPANY_INFO,
      contact: CONTACT_INFO,
      navigation: NAVIGATION,
      hero: HERO_CONTENT,
      pages: PAGE_CONTENT,
      products: PRODUCT_CATEGORIES,
      specs: TECHNICAL_SPECS,
      cta: CTA_MESSAGES,
      media: MEDIA_ASSETS,
      stats: COMPANY_STATS,
      timeline: COMPANY_TIMELINE,
      seo: SEO_CONTENT,
    }),
    []
  );

  const getPageContent = (page: string): ContentSection | null => {
    const pageKey = page as keyof typeof PAGE_CONTENT;
    return PAGE_CONTENT[pageKey]?.overview || null;
  };

  const getHeroContent = (page: string) => {
    const heroKey = page as keyof typeof HERO_CONTENT;
    return HERO_CONTENT[heroKey] || HERO_CONTENT.home;
  };

  const getSEOContent = (page: string) => {
    const seoKey = page as keyof typeof SEO_CONTENT;
    return SEO_CONTENT[seoKey] || SEO_CONTENT.home;
  };

  const getMediaAsset = (type: keyof typeof MEDIA_ASSETS, key: string): string => {
    const assetGroup = MEDIA_ASSETS[type] as Record<string, string>;
    return assetGroup[key] || '';
  };

  return {
    ...content,
    getPageContent,
    getHeroContent,
    getSEOContent,
    getMediaAsset,
  };
};

// Utility hook for specific content sections
export const usePageContent = (page: string) => {
  const { getPageContent, getHeroContent, getSEOContent } = useContent();

  return useMemo(
    () => ({
      content: getPageContent(page),
      hero: getHeroContent(page),
      seo: getSEOContent(page),
    }),
    [page, getPageContent, getHeroContent, getSEOContent]
  );
};

// Hook for navigation with active state
export const useNavigation = (currentPath: string = '/') => {
  const { navigation } = useContent();

  return useMemo(
    () =>
      navigation.map(item => ({
        ...item,
        isActive:
          item.href === currentPath || (item.href !== '/' && currentPath.startsWith(item.href)),
      })),
    [navigation, currentPath]
  );
};

// Hook for company branding
export const useBranding = () => {
  const { company } = useContent();

  return useMemo(
    () => ({
      name: company.name,
      tagline: company.tagline,
      description: company.description,
      fullName: `${company.name} - ${company.tagline}`,
      shortDescription: company.description.split('.')[0] + '.',
    }),
    [company]
  );
};

export default useContent;
