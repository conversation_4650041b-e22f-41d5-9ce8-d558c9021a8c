// Security utilities

export const sanitizeHTML = (input: string): string => {
  const div = document.createElement('div');
  div.textContent = input;
  return div.innerHTML;
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-()]/g, ''));
};

export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
    .slice(0, 1000); // Limit length
};

export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const validateCSRFToken = (token: string, storedToken: string): boolean => {
  return token === storedToken && token.length === 64;
};

export const rateLimit = (() => {
  const attempts = new Map<string, { count: number; lastAttempt: number }>();

  return (identifier: string, maxAttempts: number = 5, windowMs: number = 60000): boolean => {
    const now = Date.now();
    const userAttempts = attempts.get(identifier);

    if (!userAttempts) {
      attempts.set(identifier, { count: 1, lastAttempt: now });
      return true;
    }

    if (now - userAttempts.lastAttempt > windowMs) {
      attempts.set(identifier, { count: 1, lastAttempt: now });
      return true;
    }

    if (userAttempts.count >= maxAttempts) {
      return false;
    }

    userAttempts.count++;
    userAttempts.lastAttempt = now;
    return true;
  };
})();

export const secureLocalStorage = {
  setItem: (key: string, value: string): void => {
    try {
      const encrypted = btoa(value); // Simple encoding (use proper encryption in production)
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.warn('Failed to store item securely:', error);
    }
  },

  getItem: (key: string): string | null => {
    try {
      const encrypted = localStorage.getItem(key);
      return encrypted ? atob(encrypted) : null;
    } catch (error) {
      console.warn('Failed to retrieve item securely:', error);
      return null;
    }
  },

  removeItem: (key: string): void => {
    localStorage.removeItem(key);
  },
};

export const validateURL = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
};

export const preventXSS = (element: HTMLElement): void => {
  // Remove potentially dangerous attributes
  const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover'];
  dangerousAttrs.forEach(attr => {
    element.removeAttribute(attr);
  });

  // Remove script tags
  const scripts = element.querySelectorAll('script');
  scripts.forEach(script => script.remove());
};

export const securityHeaders = {
  'Content-Security-Policy':
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self'",
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
};

export const initializeSecurity = (): void => {
  // Add security headers to meta tags
  Object.entries(securityHeaders).forEach(([name, content]) => {
    const meta = document.createElement('meta');
    meta.httpEquiv = name;
    meta.content = content;
    document.head.appendChild(meta);
  });

  // Prevent clickjacking
  if (window.top !== window.self) {
    window.top!.location = window.self.location;
  }

  // Clear sensitive data on page unload
  window.addEventListener('beforeunload', () => {
    // Clear any sensitive form data
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      const inputs = form.querySelectorAll('input[type="password"], input[type="email"]');
      inputs.forEach(input => {
        (input as HTMLInputElement).value = '';
      });
    });
  });
};
