import { Clock, Globe, Mail, MapPin, Phone, Send } from 'lucide-react';
import React, { useState } from 'react';

const ContactSection: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    project: '',
    message: '',
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters long';
    }

    return newErrors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const formErrors = validateForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Form submitted:', formData);
      setSubmitStatus('success');

      // Reset form after successful submission
      setFormData({
        name: '',
        email: '',
        company: '',
        phone: '',
        project: '',
        message: '',
      });

      // Reset success message after 5 seconds
      setTimeout(() => setSubmitStatus('idle'), 5000);
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
      setTimeout(() => setSubmitStatus('idle'), 5000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            Get In
            <span className="block metallic-gradient font-light">Touch</span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Ready to discuss your aluminum manufacturing needs? Our team of experts is here to
            provide tailored solutions for your project.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Information */}
          <div className="animate-on-scroll">
            <h3 className="text-2xl font-light text-white mb-8">Contact Information</h3>

            <div className="space-y-6 mb-12">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">Headquarters</h4>
                  <p className="text-gray-400">
                    1234 Industrial Boulevard
                    <br />
                    Manufacturing District
                    <br />
                    New York, NY 10001
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <Phone className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">Phone</h4>
                  <p className="text-gray-400">+1 (234) 567-890</p>
                  <p className="text-gray-400">+1 (234) 567-891</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <Mail className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">Email</h4>
                  <p className="text-gray-400"><EMAIL></p>
                  <p className="text-gray-400"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <Clock className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">Business Hours</h4>
                  <p className="text-gray-400">Monday - Friday: 8:00 AM - 6:00 PM</p>
                  <p className="text-gray-400">Saturday: 9:00 AM - 4:00 PM</p>
                </div>
              </div>
            </div>

            {/* Company Info */}
            <div className="glass-effect rounded-2xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Globe className="w-5 h-5" />
                <span>WINASTRA Manufacturing</span>
              </h4>
              <p className="text-gray-400 leading-relaxed mb-4">
                Leading the industry in precision aluminum manufacturing with over two decades of
                experience. We deliver excellence through innovation, quality, and uncompromising
                attention to detail.
              </p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-white font-semibold">ISO 9001:2015</div>
                  <div className="text-gray-500">Quality Certified</div>
                </div>
                <div>
                  <div className="text-white font-semibold">AS9100D</div>
                  <div className="text-gray-500">Aerospace Standard</div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="animate-on-scroll">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:outline-none transition-colors duration-200 text-white placeholder-gray-500 ${
                      errors.name
                        ? 'border-red-500 focus:border-red-400'
                        : 'border-gray-700 focus:border-gray-400'
                    }`}
                    placeholder="Your full name"
                    aria-describedby={errors.name ? 'name-error' : undefined}
                  />
                  {errors.name && (
                    <p id="name-error" className="mt-1 text-sm text-red-400" role="alert">
                      {errors.name}
                    </p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:outline-none transition-colors duration-200 text-white placeholder-gray-500 ${
                      errors.email
                        ? 'border-red-500 focus:border-red-400'
                        : 'border-gray-700 focus:border-gray-400'
                    }`}
                    placeholder="<EMAIL>"
                    aria-describedby={errors.email ? 'email-error' : undefined}
                  />
                  {errors.email && (
                    <p id="email-error" className="mt-1 text-sm text-red-400" role="alert">
                      {errors.email}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                    Company
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none transition-colors duration-200 text-white placeholder-gray-500"
                    placeholder="Your company name"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none transition-colors duration-200 text-white placeholder-gray-500"
                    placeholder="+1 (234) 567-890"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="project" className="block text-sm font-medium text-gray-300 mb-2">
                  Project Type
                </label>
                <select
                  id="project"
                  name="project"
                  value={formData.project}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none transition-colors duration-200 text-white">
                  <option value="">Select project type</option>
                  <option value="architectural">Architectural</option>
                  <option value="aerospace">Aerospace</option>
                  <option value="marine">Marine</option>
                  <option value="industrial">Industrial</option>
                  <option value="custom">Custom Fabrication</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                  Project Details *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={5}
                  required
                  className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:outline-none transition-colors duration-200 text-white placeholder-gray-500 resize-none ${
                    errors.message
                      ? 'border-red-500 focus:border-red-400'
                      : 'border-gray-700 focus:border-gray-400'
                  }`}
                  placeholder="Please describe your project requirements, specifications, and timeline..."
                  aria-describedby={errors.message ? 'message-error' : undefined}></textarea>
                {errors.message && (
                  <p id="message-error" className="mt-1 text-sm text-red-400" role="alert">
                    {errors.message}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full py-4 px-6 rounded-lg font-medium text-lg tracking-wide flex items-center justify-center space-x-2 transition-all duration-300 ${
                  isSubmitting
                    ? 'bg-gray-600 cursor-not-allowed'
                    : 'metallic-button hover:scale-105'
                }`}>
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                    <span>Sending...</span>
                  </>
                ) : (
                  <>
                    <Send size={18} />
                    <span>Send Message</span>
                  </>
                )}
              </button>

              {/* Success/Error Messages */}
              {submitStatus === 'success' && (
                <div
                  className="mt-4 p-4 bg-green-900/50 border border-green-700 rounded-lg text-green-300"
                  role="alert">
                  <p className="font-medium">Message sent successfully!</p>
                  <p className="text-sm mt-1">
                    Thank you for your inquiry. We'll get back to you within 24 hours.
                  </p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div
                  className="mt-4 p-4 bg-red-900/50 border border-red-700 rounded-lg text-red-300"
                  role="alert">
                  <p className="font-medium">Failed to send message</p>
                  <p className="text-sm mt-1">
                    Please try again or contact us <NAME_EMAIL>
                  </p>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
