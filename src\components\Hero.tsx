import { Sparkles } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useScrollY } from '../hooks/useScroll';
import ResponsiveImage from './ResponsiveImage';

interface HeroSpec {
  icon: React.ComponentType<unknown>;
  value: string;
  label: string;
  description: string;
  detail: string;
}

interface HeroProps {
  id?: string;
  title: {
    primary: string;
    secondary: string;
  };
  subtitle: string;
  description: string;
  backgroundImage: string;
  backgroundAlt: string;
  brandMark?: string;
  specs?: HeroSpec[];
  variant?: 'standard' | 'premium';
  onExploreClick?: () => void;
  onWatchClick?: () => void;
}

const Hero: React.FC<HeroProps> = ({
  id = 'home',
  title,
  subtitle,
  description,
  backgroundImage,
  backgroundAlt,
  brandMark = 'PRECISION ALUMINUM MANUFACTURING',
  // specs = [],
  variant = 'standard',
  // onExploreClick,
  // onWatchClick,
}) => {
  const scrollY = useScrollY();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);
  // const handleExploreClick = () => {
  //   if (onExploreClick) {
  //     onExploreClick();
  //   } else {
  //     const manufacturingSection = document.getElementById('manufacturing');
  //     if (manufacturingSection) {
  //       manufacturingSection.scrollIntoView({ behavior: 'smooth' });
  //     }
  //   }
  // };
  // const handleWatchClick = () => {
  //   if (onWatchClick) onWatchClick();
  // };
  const isPremium = variant === 'premium';

  return (
    <section id={id} className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <div className="relative w-full h-full">
          <div
            style={{
              transform: `translateY(${scrollY * 0.3}px) scale(${1 + scrollY * 0.0002})`,
              filter: isPremium
                ? 'brightness(0.7) contrast(1.1)'
                : 'brightness(0.6) contrast(1.2) saturate(1.1)',
            }}>
            <ResponsiveImage
              src={backgroundImage}
              alt={backgroundAlt}
              className="w-full h-full"
              loading="eager"
              priority={true}
              width={1920}
              height={1080}
              context="hero"
              description={backgroundAlt}
            />
          </div>

          {/* Gradient overlays */}
          <div
            className={`absolute inset-0 ${isPremium
              ? 'bg-gradient-to-b from-black/80 via-black/40 to-black/90'
              : 'bg-gradient-to-b from-black/90 via-black/60 to-black/95'
              }`}></div>
          <div
            className={`absolute inset-0 ${isPremium
              ? 'bg-gradient-to-r from-black/60 via-transparent to-black/60'
              : 'bg-gradient-to-r from-black/80 via-transparent to-black/80'
              }`}></div>

          {/* Pattern Overlay */}
          {!isPremium && (
            <div
              className="absolute inset-0 opacity-10"
              style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.4) 0%, transparent 50%),
                                 radial-gradient(circle at 75% 75%, rgba(192, 192, 192, 0.3) 0%, transparent 50%)`,
                backgroundSize: '400px 400px',
                animation: 'float 8s ease-in-out infinite',
              }}></div>
          )}

          {/* Premium Pattern Overlay */}
          {isPremium && (
            <div
              className="absolute inset-0 opacity-5"
              style={{
                backgroundImage: `linear-gradient(45deg, rgba(212, 175, 55, 0.1) 25%, transparent 25%),
                                 linear-gradient(-45deg, rgba(192, 192, 192, 0.1) 25%, transparent 25%)`,
                backgroundSize: '60px 60px',
              }}></div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center max-w-7xl mx-auto px-4 pt-20">
        <div
          className={`transition-all duration-1200 ${isLoaded ? (isPremium ? 'luxury-animate-fade-up' : 'animate-fade-in') : 'opacity-0'
            }`}>
          {/* Brand Mark */}
          <div className="flex items-center justify-center mb-8">
            <Sparkles
              className={`w-6 h-6 text-yellow-400 mr-3 ${isPremium ? 'luxury-animate-float' : 'animate-float'
                }`}
            />
            <span
              className={`text-sm font-semibold tracking-wider uppercase ${isPremium ? 'luxury-caption text-gray-300' : 'text-gray-100'
                }`}>
              {brandMark}
            </span>
            <Sparkles
              className={`w-6 h-6 text-yellow-400 ml-3 ${isPremium ? 'luxury-animate-float' : 'animate-float'
                }`}
              style={{ animationDelay: '1s' }}
            />
          </div>

          {/* Main Heading */}
          <h1
            className={`text-4xl md:text-6xl lg:text-7xl font-thin mb-6 tracking-tight leading-tight ${isPremium ? 'luxury-heading-display' : ''
              }`}
          >
            <span
              className={`block drop-shadow-xl ${isPremium ? 'luxury-gradient-platinum' : 'metallic-gradient'
                }`}
            >
              {title.primary}
            </span>
            <span
              className={`block font-light mt-1 drop-shadow-lg ${isPremium ? 'luxury-gradient-gold' : 'text-white'
                }`}
            >
              {title.secondary}
            </span>
          </h1>

          {/* Subtitle */}
          <div className="mb-8" style={{ animationDelay: '0.9s' }}>
            <p
              className={`text-xl md:text-2xl mb-4 max-w-4xl mx-auto font-medium drop-shadow-lg ${isPremium ? 'luxury-body-large text-gray-200' : 'text-white'
                }`}>
              {subtitle}
            </p>
            <div className="w-32 h-0.5 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto"></div>
          </div>

          <p
            className={`text-lg mb-12 max-w-3xl mx-auto leading-relaxed drop-shadow-lg ${isPremium ? 'luxury-body text-gray-300' : 'text-gray-200'
              }`}
            style={{ animationDelay: '1.2s' }}>
            {description}
          </p>

          {/* CTA Buttons */}
          {/* <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12">
            <button
              onClick={handleExploreClick}
              className={`group relative overflow-hidden px-6 py-3 text-base md:text-lg font-semibold rounded-lg hover:scale-105 transition-transform duration-300 ${isPremium ? 'luxury-button-primary' : 'metallic-button'
                }`}
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>Explore Excellence</span>
                <ChevronDown size={18} className="group-hover:translate-y-1 transition-transform duration-300" />
              </span>
            </button>

            <button
              onClick={handleWatchClick}
              className={`group flex items-center space-x-3 px-6 py-3 rounded-lg transition-colors duration-300 ${isPremium ? 'luxury-glass-effect hover:bg-white/10' : 'glass-effect hover:bg-white/10'
                }`}
            >
              <div className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Play size={16} className="ml-1 text-yellow-400" />
              </div>
              <span className="text-base md:text-lg font-semibold text-white">Watch Process</span>
            </button>
          </div> */}

          {/* Technical Specifications */}
          {/* {specs.length > 0 && (
            <div
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto"
              style={{ animationDelay: '1.8s' }}>
              {specs.map((spec, index) => (
                <div
                  key={index}
                  className={`rounded-2xl p-8 group relative backdrop-blur-md ${isPremium ? 'luxury-glass-effect luxury-hover-lift' : 'glass-effect hover-lift'
                    }`}
                  style={{ animationDelay: `${2.1 + index * 0.2}s` }}>
                  <div className="flex items-center justify-center mb-4">
                    <spec.icon className="w-8 h-8 text-yellow-400 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <div
                    className={`text-4xl font-light mb-3 group-hover:scale-105 transition-transform duration-300 ${isPremium ? 'luxury-gradient-gold mono-font' : 'metallic-gradient mono-font'
                      }`}>
                    {spec.value}
                  </div>
                  <div className="text-sm text-gray-200 mb-2 font-semibold">{spec.label}</div>
                  <div className="text-sm text-gray-300 mb-2 font-medium">{spec.description}</div>
                  <div className="text-xs text-gray-400 leading-relaxed">{spec.detail}</div>
                </div>
              ))}
            </div>
          )} */}
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
        <div
          className={`flex flex-col items-center space-y-3 ${isPremium ? 'luxury-animate-float' : 'animate-float'
            }`}>
          <div className="w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-300 rounded-full mt-2 animate-bounce"></div>
          </div>
        </div>
      </div>

      {/* Ambient Light Effects */}
      <div
        className={`absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl ${isPremium ? 'luxury-animate-float' : 'animate-float'
          }`}></div>
      <div
        className={`absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl ${isPremium ? 'luxury-animate-float' : 'animate-float'
          }`}
        style={{ animationDelay: '2s' }}></div>
    </section>
  );
};

export default Hero;
