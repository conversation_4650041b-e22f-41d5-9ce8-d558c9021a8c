import { Mail, Menu, Phone, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface FloatingNavigationProps {
  currentPage?: string;
  onNavigate?: (page: string) => void;
}

const FloatingNavigation: React.FC<FloatingNavigationProps> = ({
  currentPage = 'home',
  onNavigate,
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const menuItems = [
    { name: 'Home', href: '#home', page: 'home' },
    { name: 'About', href: '#about', page: 'about' },
    { name: 'Services', href: '#services', page: 'services' },
    { name: 'Quality', href: '#quality', page: 'quality' },
    { name: 'Sustainability', href: '#sustainability', page: 'sustainability' },
    { name: 'News', href: '#news', page: 'news' },
    { name: 'Careers', href: '#careers', page: 'careers' },
    { name: 'Contact', href: '#contact', page: 'home' },
  ];

  const handleNavigation = (item: (typeof menuItems)[0]) => {
    if (onNavigate && item.page !== 'home') {
      onNavigate(item.page);
    } else if (item.page === 'home' && currentPage !== 'home') {
      onNavigate?.('home');
    } else if (item.href.startsWith('#') && currentPage === 'home') {
      // Scroll to section on current page
      setTimeout(() => {
        const element = document.querySelector(item.href);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
    } else if (item.href.startsWith('#') && currentPage !== 'home') {
      // Navigate to home first, then scroll
      onNavigate?.('home');
      setTimeout(() => {
        const element = document.querySelector(item.href);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 300);
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      <nav
        role="navigation"
        aria-label="Main navigation"
        className={`fixed top-6 left-1/2 transform -translate-x-1/2 z-40 transition-all duration-500 ${
          isScrolled ? 'glass-effect rounded-full px-8 py-4 shadow-2xl' : 'bg-transparent px-8 py-6'
        }`}>
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button
              onClick={() => handleNavigation({ name: 'Home', href: '#home', page: 'home' })}
              className="text-xl font-bold metallic-gradient tracking-wider">
              WINASTRA
            </button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8 ml-12">
            {menuItems.map(item => (
              <button
                key={item.name}
                onClick={() => handleNavigation(item)}
                className={`text-sm font-medium tracking-wide relative group transition-colors duration-300 ${
                  currentPage === item.page ? 'text-white' : 'text-gray-300 hover:text-white'
                }`}>
                {item.name}
                <span
                  className={`absolute -bottom-1 left-0 h-0.5 bg-gradient-to-r from-gray-400 to-yellow-500 transition-all duration-300 ${
                    currentPage === item.page ? 'w-full' : 'w-0 group-hover:w-full'
                  }`}></span>
              </button>
            ))}
          </div>

          {/* Contact Info */}
          {/* <div className="hidden lg:flex items-center space-x-4 ml-8">
            <a
              href="tel:+1234567890"
              className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300">
              <Phone size={16} />
              <span className="text-sm">+1 (234) 567-890</span>
            </a>
          </div> */}

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-300 hover:text-white transition-colors duration-300"
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
              aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}>
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-30 lg:hidden"
          role="dialog"
          aria-modal="true"
          aria-labelledby="mobile-menu-title">
          <div
            className="absolute inset-0 bg-black/90 backdrop-blur-md"
            onClick={() => setIsMobileMenuOpen(false)}></div>
          <div
            id="mobile-menu"
            className="relative flex flex-col items-center justify-center h-full space-y-8">
            <h2 id="mobile-menu-title" className="sr-only">
              Navigation Menu
            </h2>
            {menuItems.map((item, index) => (
              <button
                key={item.name}
                onClick={() => handleNavigation(item)}
                className={`text-2xl font-light transition-all duration-300 hover:scale-110 ${
                  currentPage === item.page ? 'text-white' : 'text-gray-300 hover:text-white'
                }`}
                style={{ animationDelay: `${index * 0.1}s` }}>
                {item.name}
              </button>
            ))}
            <div className="flex items-center space-x-6 mt-12">
              <a
                href="tel:+1234567890"
                className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300">
                <Phone size={20} />
                <span>+1 (234) 567-890</span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300">
                <Mail size={20} />
                <span>Contact</span>
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FloatingNavigation;
