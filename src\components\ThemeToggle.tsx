import { Moon, Sun } from 'lucide-react';
import React from 'react';
import { useTheme } from '../hooks/useTheme';
import { useUnifiedContent } from '../hooks/useUnifiedContent';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'button' | 'switch';
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'md',
  variant = 'icon'
}) => {
  const { theme, toggleTheme } = useTheme();
  const { ui } = useUnifiedContent();

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24
  };

  if (variant === 'switch') {
    return (
      <button
        onClick={toggleTheme}
        className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-purple focus:ring-offset-2 ${theme === 'dark'
          ? 'bg-primary-purple'
          : 'bg-gray-300'
          } ${className}`}
        aria-label={theme === 'light' ? ui.theme.switchToDark : ui.theme.switchToLight}
      >
        <span
          className={`inline-block w-4 h-4 transform transition-transform duration-200 bg-white rounded-full ${theme === 'dark' ? 'translate-x-6' : 'translate-x-1'
            }`}
        />
        <Sun
          size={12}
          className={`absolute left-1 text-yellow-500 transition-opacity duration-200 ${theme === 'dark' ? 'opacity-0' : 'opacity-100'
            }`}
        />
        <Moon
          size={12}
          className={`absolute right-1 text-gray-700 transition-opacity duration-200 ${theme === 'dark' ? 'opacity-100' : 'opacity-0'
            }`}
        />
      </button>
    );
  }

  if (variant === 'button') {
    return (
      <button
        onClick={toggleTheme}
        className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-purple focus:ring-offset-2 ${theme === 'dark'
          ? 'bg-gray-800 text-white hover:bg-gray-700'
          : 'bg-white text-gray-900 hover:bg-gray-50 shadow-sm border border-gray-200'
          } ${className}`}
        aria-label={theme === 'light' ? ui.theme.switchToDark : ui.theme.switchToLight}
      >
        {theme === 'light' ? (
          <>
            <Moon size={iconSizes[size]} />
            <span className="text-sm font-medium">{ui.theme.darkMode}</span>
          </>
        ) : (
          <>
            <Sun size={iconSizes[size]} />
            <span className="text-sm font-medium">{ui.theme.lightMode}</span>
          </>
        )}
      </button>
    );
  }

  // Default icon variant
  return (
    <button
      onClick={toggleTheme}
      className={`${sizeClasses[size]} rounded-full transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-purple focus:ring-offset-2 ${theme === 'dark'
        ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700'
        : 'bg-white text-gray-700 hover:bg-gray-50 shadow-sm border border-gray-200'
        } flex items-center justify-center ${className}`}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        <Moon size={iconSizes[size]} />
      ) : (
        <Sun size={iconSizes[size]} />
      )}
    </button>
  );
};

export default ThemeToggle;
